{% extends "base_modern.html" %} {% block title %}TradingAgents - Tableau de Bord{% endblock %} {% block extra_head %} <style> .dashboard-header { background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%); color: var(--text-inverse); padding: var(--space-12) 0; margin-bottom: var(--space-8); border-radius: var(--radius-2xl); position: relative; overflow: hidden; } .dashboard-header::before { content: ''; position: absolute; top: 0; right: 0; width: 200px; height: 200px; background: rgba(255, 255, 255, 0.1); border-radius: 50%; transform: translate(50px, -50px); } .dashboard-section { margin-bottom: var(--space-8); } .chart-container { background-color: var(--bg-card); border-radius: var(--radius-lg); padding: var(--space-6); margin-bottom: var(--space-6); box-shadow: var(--shadow-sm); border: 1px solid var(--border-color); } .chart-wrapper { height: 400px; position: relative; } @media (max-width: 768px) { .dashboard-header { padding: var(--space-8) 0; } .chart-wrapper { height: 300px; } } </style> {% endblock %} {% block content %} <div class="container"> <!-- En-tête du tableau de bord --> <div class="dashboard-header"> <div class="text-center"> <h1><i class="fas fa-tachometer-alt"></i> Tableau de Bord</h1> <p class="text-lg opacity-90"> Visualisez les performances et métriques de vos agents de trading en temps réel </p> </div> </div> <!-- Métriques principales --> <div class="dashboard-section"> <div class="metrics-grid"> <div class="metric-card"> <div class="metric-icon"> <i class="fas fa-chart-line"></i> </div> <div class="metric-content"> <div class="metric-value" id="totalAnalyses">0</div> <div class="metric-label">Analyses Totales</div> <div class="metric-change positive"> <i class="fas fa-arrow-up"></i> +12% ce mois </div> </div> </div> <div class="metric-card"> <div class="metric-icon success"> <i class="fas fa-bullseye"></i> </div> <div class="metric-content"> <div class="metric-value" id="successRate">0%</div> <div class="metric-label">Taux de Réussite</div> <div class="metric-change positive"> <i class="fas fa-arrow-up"></i> +5% cette semaine </div> </div> </div> <div class="metric-card"> <div class="metric-icon warning"> <i class="fas fa-clock"></i> </div> <div class="metric-content"> <div class="metric-value" id="avgTime">0s</div> <div class="metric-label">Temps Moyen</div> <div class="metric-change neutral"> <i class="fas fa-minus"></i> Stable </div> </div> </div> <div class="metric-card"> <div class="metric-icon info"> <i class="fas fa-robot"></i> </div> <div class="metric-content"> <div class="metric-value" id="activeAgents">0</div> <div class="metric-label">Agents Actifs</div> <div class="metric-change positive"> <i class="fas fa-arrow-up"></i> 4 en ligne </div> </div> </div> </div> </div> <!-- Graphiques --> <div class="dashboard-section"> <div class="grid grid-cols-1 lg:grid-cols-3 gap-8"> <div class="lg:span:w-2/12"> <div class="chart-container"> <div class="chart-header"> <h3 class="chart-title"> <i class="fas fa-chart-area"></i> Performance des Analyses </h3> <div class="chart-controls"> <div class="chart-period-selector"> <button class="period-btn active" data-period="7d">7J</button> <button class="period-btn" data-period="30d">30J</button> <button class="period-btn" data-period="90d">90J</button> </div> </div> </div> <div class="chart-wrapper"> <canvas id="performanceChart"></canvas> </div> </div> </div> <div> <div class="chart-container"> <div class="chart-header"> <h3 class="chart-title"> <i class="fas fa-pie-chart"></i> Répartition des Décisions </h3> </div> <div class="chart-wrapper small"> <canvas id="decisionsChart"></canvas> </div> </div> </div> </div> </div> <!-- Détails de l'analyse sélectionnée --> <div class="grid gap-6 mt-6"> <div class="col-12"> <div class="card" id="analysisDetailsCard" style="display: none;"> <div class="card-header"> <h3 <i class="fas fa-microscope mr-2"></i>Détails de l'Analyse </h3> </div> <div class="card-body" id="analysisDetailsContent"> <!-- Le contenu sera chargé dynamiquement --> </div> </div> </div> </div> <!-- Liste des analyses --> <div class="grid gap-6 mt-6"> <div class="col-12"> <div class="card"> <div class="card-header flex justify-between items-center"> <h4 class="mb-0"> <i class="fas fa-list mr-2"></i>Historique des Analyses </h4> <button class="btn btn btn-secondary btn-sm" onclick="refreshAnalyses()"> <i class="fas fa-sync-alt mr-1"></i>Actualiser </button> </div> <div class="card-body"> <div class="table-responsive"> <table class="table table-hover" id="analysesTable"> <thead> <tr> <th>Ticker</th> <th>Date d'Analyse</th> <th>Décision</th> <th>Timestamp</th> <th>Statut</th> <th>Actions</th> </tr> </thead> <tbody id="analysesTableBody"> <tr> <td colspan="6" class="text-center"> <div class="loading-spinner mr-2"></div> Chargement des analyses... </td> </tr> </tbody> </table> </div> </div> </div> </div> </div> <!-- Modal pour les détails complets --> <div class="modal fade" id="analysisModal" tabindex="-1"> <div class="modal-dialog modal-xl"> <div class="modal-content"> <div class="modal-header"> <h5 class="modal-title"> <i class="fas fa-chart-line mr-2"></i>Détails Complets de l'Analyse </h5> <button type="button" class="btn-close" data-bs-dismiss="modal"></button> </div> <div class="modal-body" id="modalAnalysisContent"> <!-- Contenu chargé dynamiquement --> </div> <div class="modal-footer"> <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button> <button type="button" class="btn btn-primary" onclick="exportAnalysis()"> <i class="fas fa-download mr-1"></i>Exporter </button> </div> </div> </div> </div> {% endblock %} {% block extra_js %} <script> let performanceChart = null; let decisionsChart = null; let currentAnalysisData = null; document.addEventListener('DOMContentLoaded', function() { // Initialiser les graphiques initializeCharts(); // Charger les données loadDashboardData(); // Vérifier s'il y a un paramètre de session dans l'URL const urlParams = new URLSearchParams(window.location.search); const sessionId = urlParams.get('session'); if (sessionId) { loadAnalysisDetails(sessionId); } // Actualiser les données toutes les 30 secondes setInterval(loadDashboardData, 30000); }); function initializeCharts() { // Graphique des performances const performanceCtx = document.getElementById('performanceChart').getContext('2d'); performanceChart = new Chart(performanceCtx, { type: 'line', data: { labels: [], datasets: [{ label: 'Analyses par Jour', data: [], borderColor: 'rgb(52, 152, 219)', backgroundColor: 'rgba(52, 152, 219, 0.1)', tension: 0.4 }] }, options: { responsive: true, maintainAspectRatio: false, scales: { y: { beginAtZero: true } } } }); // Graphique des décisions const decisionsCtx = document.getElementById('decisionsChart').getContext('2d'); decisionsChart = new Chart(decisionsCtx, { type: 'doughnut', data: { labels: ['BUY', 'SELL', 'HOLD'], datasets: [{ data: [0, 0, 0], backgroundColor: [ 'rgba(39, 174, 96, 0.8)', 'rgba(231, 76, 60, 0.8)', 'rgba(243, 156, 18, 0.8)' ], borderColor: [ 'rgba(39, 174, 96, 1)', 'rgba(231, 76, 60, 1)', 'rgba(243, 156, 18, 1)' ], borderWidth: 2 }] }, options: { responsive: true, maintainAspectRatio: false, plugins: { legend: { position: 'bottom' } } } }); } function loadDashboardData() { fetch('/api/list_results') .then(response => response.json()) .then(analyses => { updateMetrics(analyses); updateCharts(analyses); updateAnalysesTable(analyses); }) .catch(error => { console.error('Erreur lors du chargement des données:', error); showAlert('Erreur lors du chargement des données du tableau de bord', 'danger'); }); } function updateMetrics(analyses) { // Total des analyses document.getElementById('totalAnalyses').textContent = analyses.length; // Taux de réussite (exemple - à adapter selon vos critères) const successfulAnalyses = analyses.filter(a => a.decision && a.decision !== 'ERROR').length; const successRate = analyses.length > 0 ? Math.round((successfulAnalyses / analyses.length) * 100) : 0; document.getElementById('successRate').textContent = successRate + '%'; // Temps moyen (simulé - à implémenter avec de vraies données) document.getElementById('avgTime').textContent = '45s'; // Agents actifs (simulé) document.getElementById('activeAgents').textContent = '8'; } function updateCharts(analyses) { // Grouper les analyses par date const analysesByDate = {}; const decisionCounts = { BUY: 0, SELL: 0, HOLD: 0 }; analyses.forEach(analysis => { // Grouper par date const date = analysis.trade_date; analysesByDate[date] = (analysesByDate[date] || 0) + 1; // Compter les décisions if (analysis.decision) { const decision = analysis.decision.toUpperCase(); if (decisionCounts.hasOwnProperty(decision)) { decisionCounts[decision]++; } } }); // Mettre à jour le graphique des performances const dates = Object.keys(analysesByDate).sort(); const counts = dates.map(date => analysesByDate[date]); performanceChart.data.labels = dates; performanceChart.data.datasets[0].data = counts; performanceChart.update(); // Mettre à jour le graphique des décisions decisionsChart.data.datasets[0].data = [ decisionCounts.BUY, decisionCounts.SELL, decisionCounts.HOLD ]; decisionsChart.update(); } function updateAnalysesTable(analyses) { const tbody = document.getElementById('analysesTableBody'); if (analyses.length === 0) { tbody.innerHTML = '<tr><td colspan="6" class="text-center text-secondary">Aucune analyse trouvée</td></tr>'; return; } // Trier par timestamp décroissant const sortedAnalyses = analyses.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp)); let html = ''; sortedAnalyses.forEach(analysis => { const statusClass = analysis.decision ? 'success' : 'warning'; const statusText = analysis.decision ? 'Terminé' : 'En cours'; const decisionBadge = getDecisionBadge(analysis.decision); html += ` <tr onclick="loadAnalysisDetails('${analysis.session_id}')" style="cursor: pointer;"> <td><strong>${analysis.ticker}</strong></td> <td>${analysis.trade_date}</td> <td>${decisionBadge}</td> <td>${new Date(analysis.timestamp).toLocaleString('fr-FR')}</td> <td><span class="badge bg-${statusClass}">${statusText}</span></td> <td> <button class="btn btn-sm btn btn-secondary" onclick="event.stopPropagation(); viewFullAnalysis('${analysis.session_id}')"> <i class="fas fa-eye"></i> </button> <button class="btn btn-sm btn btn-secondary" onclick="event.stopPropagation(); exportAnalysis('${analysis.session_id}')"> <i class="fas fa-download"></i> </button> </td> </tr> `; }); tbody.innerHTML = html; } function getDecisionBadge(decision) { if (!decision) return '<span class="badge bg-secondary">N/A</span>'; const decisionUpper = decision.toUpperCase(); let badgeClass = 'secondary'; if (decisionUpper.includes('BUY')) badgeClass = 'success'; else if (decisionUpper.includes('SELL')) badgeClass = 'danger'; else if (decisionUpper.includes('HOLD')) badgeClass = 'warning'; return `<span class="badge bg-${badgeClass}">${decision}</span>`; } function loadAnalysisDetails(sessionId) { fetch(`/api/get_results/${sessionId}`) .then(response => response.json()) .then(data => { if (data.error) { showAlert('Erreur: ' + data.error, 'danger'); return; } currentAnalysisData = data; displayAnalysisDetails(data); }) .catch(error => { console.error('Erreur lors du chargement des détails:', error); showAlert('Erreur lors du chargement des détails de l\'analyse', 'danger'); }); } function displayAnalysisDetails(data) { const card = document.getElementById('analysisDetailsCard'); const content = document.getElementById('analysisDetailsContent'); let html = ` <div class="grid gap-6"> <div class="md:w-1/2"> <h5><i class="fas fa-info-circle mr-2"></i>Informations Générales</h5> <table class="table table-sm"> <tr><td><strong>Ticker:</strong></td><td>${data.ticker}</td></tr> <tr><td><strong>Date d'Analyse:</strong></td><td>${data.trade_date}</td></tr> <tr><td><strong>Décision Finale:</strong></td><td>${getDecisionBadge(data.decision)}</td></tr> <tr><td><strong>Session ID:</strong></td><td><code>${data.session_id}</code></td></tr> <tr><td><strong>Timestamp:</strong></td><td>${new Date(data.timestamp).toLocaleString('fr-FR')}</td></tr> </table> </div> <div class="md:w-1/2"> <h5><i class="fas fa-cogs mr-2"></i>Configuration</h5> <table class="table table-sm"> <tr><td><strong>Analystes:</strong></td><td>${data.config?.selected_analysts?.join(', ') || 'N/A'}</td></tr> <tr><td><strong>Tours de Débat:</strong></td><td>${data.config?.max_debate_rounds || 'N/A'}</td></tr> <tr><td><strong>LLM Rapide:</strong></td><td>${data.config?.quick_think_llm || 'N/A'}</td></tr> <tr><td><strong>LLM Profond:</strong></td><td>${data.config?.deep_think_llm || 'N/A'}</td></tr> </table> </div> </div> `; // Ajouter les rapports si disponibles if (data.final_state) { html += '<hr><h5><i class="fas fa-file-alt mr-2"></i>Rapports des Agents</h5>'; html += '<div class="grid gap-6">'; const reports = [ { key: 'market_report', title: 'Analyste Marché', icon: 'chart-line' }, { key: 'sentiment_report', title: 'Analyste Social', icon: 'users' }, { key: 'news_report', title: 'Analyste Actualités', icon: 'newspaper' }, { key: 'fundamentals_report', title: 'Analyste Fondamental', icon: 'calculator' } ]; reports.forEach(report => { if (data.final_state[report.key]) { html += ` <div class="md:w-1/2 mb-4"> <div class="card"> <div class="card-header bg-light"> <h6 class="mb-0"><i class="fas fa-${report.icon} mr-2"></i>${report.title}</h6> </div> <div class="card-body"> <div class="report-content" style="max-height: 200px; overflow-y: auto;"> ${data.final_state[report.key].substring(0, 300)}... </div> </div> </div> </div> `; } }); html += '</div>'; } content.innerHTML = html; card.style.display = 'block'; // Faire défiler vers la carte card.scrollIntoView({ behavior: 'smooth' }); } function viewFullAnalysis(sessionId) { loadAnalysisDetails(sessionId); // Afficher le modal avec tous les détails setTimeout(() => { if (currentAnalysisData) { displayFullAnalysisModal(currentAnalysisData); } }, 500); } function displayFullAnalysisModal(data) { const modalContent = document.getElementById('modalAnalysisContent'); // Créer un contenu détaillé pour le modal let html = ` <div class="grid gap-6 mb-4"> <div class="col-12"> <h6>Informations de l'Analyse</h6> <p><strong>Ticker:</strong> ${data.ticker} | <strong>Date:</strong> ${data.trade_date} | <strong>Décision:</strong> ${getDecisionBadge(data.decision)}</p> </div> </div> `; if (data.final_state) { // Afficher tous les rapports complets const allReports = [ { key: 'market_report', title: 'Rapport de l\'Analyste Marché' }, { key: 'sentiment_report', title: 'Rapport de l\'Analyste Social' }, { key: 'news_report', title: 'Rapport de l\'Analyste Actualités' }, { key: 'fundamentals_report', title: 'Rapport de l\'Analyste Fondamental' }, { key: 'investment_plan', title: 'Plan d\'Investissement' }, { key: 'trader_investment_plan', title: 'Plan du Trader' }, { key: 'final_trade_decision', title: 'Décision Finale de Trading' } ]; allReports.forEach(report => { if (data.final_state[report.key]) { html += ` <div class="mb-6"> <h6 class="border-bottom pb-2">${report.title}</h6> <div class="bg-light p-3 rounded"> <pre style="white-space: pre-wrap; font-family: inherit;">${data.final_state[report.key]}</pre> </div> </div> `; } }); } modalContent.innerHTML = html; // Afficher le modal const modal = new bootstrap.Modal(document.getElementById('analysisModal')); modal.show(); } function refreshAnalyses() { loadDashboardData(); showAlert('Données actualisées', 'success'); } function exportAnalysis(sessionId = null) { const dataToExport = sessionId ? (currentAnalysisData?.session_id === sessionId ? currentAnalysisData : null) : currentAnalysisData; if (!dataToExport) { showAlert('Aucune analyse sélectionnée pour l\'export', 'warning'); return; } // Créer et télécharger le fichier JSON const dataStr = JSON.stringify(dataToExport, null, 2); const dataBlob = new Blob([dataStr], {type: 'application/json'}); const link = document.createElement('a'); link.href = URL.createObjectURL(dataBlob); link.download = `analysis_${dataToExport.ticker}_${dataToExport.trade_date}.json`; link.click(); showAlert('Analyse exportée avec succès', 'success'); } </script> {% endblock %} 