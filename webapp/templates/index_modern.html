{% extends "base_modern.html" %} {% block title %}TradingAgents - Analyses Intelligentes{% endblock %} {% block extra_head %} <style> .hero-section { background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%); color: var(--text-inverse); padding: var(--space-16) 0; margin-bottom: var(--space-12); position: relative; overflow: hidden; } .hero-section::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>'); opacity: 0.3; } .hero-content { position: relative; z-index: 1; text-align: center; max-width: 800px; margin: 0 auto; } .hero-title { font-size: var(--text-4xl); font-weight: 700; margin-bottom: var(--space-6); line-height: 1.2; } .hero-subtitle { font-size: var(--text-xl); opacity: 0.9; margin-bottom: var(--space-8); line-height: 1.6; } .quick-analysis { background-color: var(--bg-card); border-radius: var(--radius-2xl); padding: var(--space-8); box-shadow: var(--shadow-xl); margin-bottom: var(--space-12); } .analysis-form { display: grid; grid-template-columns: 1fr auto auto; gap: var(--space-4); align-items: end; } .ticker-input { position: relative; } .ticker-input input { font-size: var(--text-lg); font-weight: 600; text-transform: uppercase; padding: var(--space-4) var(--space-6); } .analysis-options { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--space-4); margin-top: var(--space-6); } .option-card { background-color: var(--bg-secondary); border: 2px solid transparent; border-radius: var(--radius-lg); padding: var(--space-4); cursor: pointer; transition: all var(--transition-fast); text-align: center; } .option-card:hover { border-color: var(--primary-color); background-color: var(--primary-light); } .option-card.selected { border-color: var(--primary-color); background-color: var(--primary-light); } .recent-analyses { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: var(--space-6); } .analysis-card { background-color: var(--bg-card); border: 1px solid var(--border-color); border-radius: var(--radius-lg); padding: var(--space-6); transition: all var(--transition-normal); cursor: pointer; } .analysis-card:hover { box-shadow: var(--shadow-lg); transform: translateY(-2px); } .analysis-header { display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--space-4); } .analysis-symbol { font-size: var(--text-xl); font-weight: 700; color: var(--primary-color); } .analysis-decision { padding: var(--space-1) var(--space-3); border-radius: var(--radius-full); font-size: var(--text-xs); font-weight: 600; text-transform: uppercase; } .decision-buy { background-color: rgba(16, 185, 129, 0.1); color: var(--success-color); } .decision-sell { background-color: rgba(239, 68, 68, 0.1); color: var(--error-color); } .decision-hold { background-color: var(--bg-tertiary); color: var(--text-secondary); } .analysis-summary { font-size: var(--text-sm); color: var(--text-secondary); line-height: 1.5; margin-bottom: var(--space-3); } .analysis-meta { display: flex; align-items: center; justify-content: space-between; font-size: var(--text-xs); color: var(--text-secondary); } @media (max-width: 768px) { .hero-title { font-size: var(--text-3xl); } .hero-subtitle { font-size: var(--text-lg); } .analysis-form { grid-template-columns: 1fr; gap: var(--space-4); } .quick-analysis { padding: var(--space-6); } } </style> {% endblock %} {% block content %} <!-- Section héro --> <section class="hero-section"> <div class="container"> <div class="hero-content"> <h1 class="hero-title">Analyses de Trading Intelligentes</h1> <p class="hero-subtitle"> Exploitez la puissance de l'intelligence artificielle pour analyser les marchés financiers et prendre des décisions de trading éclairées. </p> </div> </div> </section> <!-- Analyse rapide --> <div class="container"> <div class="quick-analysis"> <h2 class="text-center mb-6">Analyse Rapide</h2> <form id="quickAnalysisForm" class="analysis-form"> <div class="ticker-input"> <label class="form-label">Symbole à analyser</label> <input type="text" class="form-control" id="ticker" placeholder="SPY, AAPL, TSLA..." required autocomplete="off"> </div> <div class="form-group"> <label class="form-label">Profondeur</label> <select class="form-control form-control form-select" id="depth"> <option value="1">Rapide (1 tour)</option> <option value="2" selected>Standard (2 tours)</option> <option value="3">Approfondie (3 tours)</option> </select> </div> <button type="submit" class="btn btn-primary btn-lg"> <i class="fas fa-chart-line"></i> Analyser </button> </form> <!-- Options d'analyse --> <div class="analysis-options"> <div class="option-card" data-analyst="market"> <i class="fas fa-chart-bar text-2xl mb-4"></i> <h6>Analyste Marché</h6> <p class="text-sm text-secondary">Analyse technique et tendances</p> </div> <div class="option-card" data-analyst="social"> <i class="fas fa-users text-2xl mb-4"></i> <h6>Analyste Social</h6> <p class="text-sm text-secondary">Sentiment des réseaux sociaux</p> </div> <div class="option-card" data-analyst="news"> <i class="fas fa-newspaper text-2xl mb-4"></i> <h6>Analyste Actualités</h6> <p class="text-sm text-secondary">Impact des nouvelles</p> </div> <div class="option-card" data-analyst="fundamentals"> <i class="fas fa-calculator text-2xl mb-4"></i> <h6>Analyste Fondamental</h6> <p class="text-sm text-secondary">Données financières</p> </div> </div> </div> <!-- Analyses récentes --> <section class="mt-22"> <div class="flex justify-between items-center mb-8"> <h2>Analyses Récentes</h2> <a href="/dashboard" class="btn btn-secondary"> <i class="fas fa-eye"></i> Voir tout </a> </div> <div class="recent-analyses" id="recentAnalyses"> <!-- Chargé dynamiquement --> </div> </section> </div> <!-- Modal de progression --> <div class="modal-overlay" id="analysisModal"> <div class="modal"> <div class="modal-header"> <h3 class="modal-title">Analyse en cours</h3> </div> <div class="modal-body"> <div class="text-center"> <div class="loading-spinner-lg mb-6"></div> <h4 id="analysisSymbol">SPY</h4> <p class="text-secondary mb-6">Analyse par les agents IA...</p> <div class="progress mb-6"> <div class="progress-bar animated" id="analysisProgress" style="width: 0%"></div> </div> <div id="analysisSteps"> <!-- Étapes de progression --> </div> </div> </div> </div> </div> {% endblock %} {% block extra_scripts %} <script> document.addEventListener('DOMContentLoaded', function() { // Initialisation initializeAnalysisOptions(); loadRecentAnalyses(); // Gestionnaire de formulaire document.getElementById('quickAnalysisForm').addEventListener('submit', handleQuickAnalysis); }); function initializeAnalysisOptions() { const options = document.querySelectorAll('.option-card'); // Sélectionner toutes les options par défaut options.forEach(option => { option.classList.add('selected'); option.addEventListener('click', function() { this.classList.toggle('selected'); }); }); } async function handleQuickAnalysis(event) { event.preventDefault(); const ticker = document.getElementById('ticker').value.trim().toUpperCase(); const depth = document.getElementById('depth').value; const selectedAnalysts = Array.from(document.querySelectorAll('.option-card.selected')) .map(card => card.dataset.analyst); if (!ticker) { showNotification('Veuillez entrer un symbole', 'warning'); return; } if (selectedAnalysts.length === 0) { showNotification('Veuillez sélectionner au moins un analyste', 'warning'); return; } // Afficher la modal de progression showAnalysisModal(ticker); try { // Démarrer l'analyse const response = await fetch('/api/start_analysis', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ ticker: ticker, selected_analysts: selectedAnalysts, max_debate_rounds: parseInt(depth) }) }); const result = await response.json(); if (result.session_id) { // Suivre la progression trackAnalysisProgress(result.session_id); } else { throw new Error(result.error || 'Erreur lors du démarrage de l\'analyse'); } } catch (error) { console.error('Erreur analyse:', error); hideAnalysisModal(); showNotification('Erreur lors de l\'analyse: ' + error.message, 'error'); } } function showAnalysisModal(symbol) { document.getElementById('analysisSymbol').textContent = symbol; document.getElementById('analysisProgress').style.width = '0%'; document.getElementById('analysisSteps').innerHTML = ''; document.getElementById('analysisModal').classList.add('active'); } function hideAnalysisModal() { document.getElementById('analysisModal').classList.remove('active'); } function trackAnalysisProgress(sessionId) { const socket = io(); let progress = 0; socket.emit('join_session', sessionId); socket.on('analysis_progress', function(data) { progress += 20; // Simulation de progression updateProgress(Math.min(progress, 90)); addProgressStep(data.message || 'Analyse en cours...'); }); socket.on('analysis_complete', function(data) { updateProgress(100); addProgressStep('Analyse terminée!'); setTimeout(() => { hideAnalysisModal(); showNotification('Analyse terminée avec succès!', 'success'); loadRecentAnalyses(); // Recharger les analyses récentes }, 1500); }); socket.on('analysis_error', function(data) { hideAnalysisModal(); showNotification('Erreur: ' + data.error, 'error'); }); } function updateProgress(percentage) { document.getElementById('analysisProgress').style.width = percentage + '%'; } function addProgressStep(message) { const stepsContainer = document.getElementById('analysisSteps'); const step = document.createElement('div'); step.className = 'flex items-center gap-2 mb-4 text-sm'; step.innerHTML = ` <i class="fas fa-check text-success"></i> <span>${message}</span> `; stepsContainer.appendChild(step); // Faire défiler vers le bas stepsContainer.scrollTop = stepsContainer.scrollHeight; } async function loadRecentAnalyses() { try { const response = await fetch('/api/list_results'); const data = await response.json(); const container = document.getElementById('recentAnalyses'); if (data.results && data.results.length > 0) { container.innerHTML = data.results.slice(0, 6).map(analysis => createAnalysisCard(analysis) ).join(''); } else { container.innerHTML = ` <div class="col-span-full text-center py-12"> <i class="fas fa-chart-line text-4xl text-secondary mb-6"></i> <h4>Aucune analyse récente</h4> <p class="text-secondary">Commencez par analyser un symbole ci-dessus</p> </div> `; } } catch (error) { console.error('Erreur chargement analyses:', error); } } function createAnalysisCard(analysis) { const decision = analysis.final_decision || 'HOLD'; const decisionClass = `decision-${decision.toLowerCase()}`; const date = new Date(analysis.timestamp).toLocaleDateString('fr-FR'); const time = new Date(analysis.timestamp).toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' }); return ` <div class="analysis-card" onclick="viewAnalysis('${analysis.session_id}')"> <div class="analysis-header"> <div class="analysis-symbol">${analysis.ticker}</div> <div class="analysis-decision ${decisionClass}">${decision}</div> </div> <div class="analysis-summary"> ${analysis.summary || 'Analyse complète disponible'} </div> <div class="analysis-meta"> <span><i class="fas fa-calendar"></i> ${date}</span> <span><i class="fas fa-clock"></i> ${time}</span> </div> </div> `; } function viewAnalysis(sessionId) { window.location.href = `/dashboard?session=${sessionId}`; } </script> {% endblock %} 