{% extends "base_modern.html" %} {% block title %}TradingAgents - Backtesting{% endblock %} {% block extra_head %} <style> .backtesting-header { background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%); color: var(--text-inverse); padding: var(--space-12) 0; margin-bottom: var(--space-8); border-radius: var(--radius-2xl); position: relative; overflow: hidden; } .backtesting-header::before { content: ''; position: absolute; top: 0; right: 0; width: 200px; height: 200px; background: rgba(255, 255, 255, 0.1); border-radius: 50%; transform: translate(50px, -50px); } .backtest-form { background-color: var(--bg-card); border-radius: var(--radius-xl); padding: var(--space-8); box-shadow: var(--shadow-lg); margin-bottom: var(--space-8); } .form-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: var(--space-6); } .symbol-input { position: relative; } .symbol-tags { display: flex; flex-wrap: wrap; gap: var(--space-2); margin-top: var(--space-3); } .symbol-tag { background-color: var(--primary-light); color: var(--primary-color); padding: var(--space-1) var(--space-3); border-radius: var(--radius-full); font-size: var(--text-sm); display: flex; align-items: center; gap: var(--space-2); } .symbol-tag button { background: none; border: none; color: inherit; cursor: pointer; padding: 0; font-size: var(--text-xs); } .backtest-list { display: grid; gap: var(--space-6); } .backtest-card { background-color: var(--bg-card); border: 1px solid var(--border-color); border-radius: var(--radius-lg); padding: var(--space-6); transition: all var(--transition-normal); } .backtest-card:hover { box-shadow: var(--shadow-lg); transform: translateY(-2px); } .backtest-header { display: flex; justify-content: space-between; align-items: start; margin-bottom: var(--space-4); } .backtest-title { font-size: var(--text-xl); font-weight: 600; color: var(--text-primary); margin-bottom: var(--space-1); } .backtest-meta { font-size: var(--text-sm); color: var(--text-secondary); } .backtest-status { padding: var(--space-1) var(--space-3); border-radius: var(--radius-full); font-size: var(--text-xs); font-weight: 600; text-transform: uppercase; } .status-completed { background-color: rgba(16, 185, 129, 0.1); color: var(--success-color); } .status-running { background-color: rgba(59, 130, 246, 0.1); color: var(--info-color); } .status-failed { background-color: rgba(239, 68, 68, 0.1); color: var(--error-color); } .backtest-metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: var(--space-4); margin-top: var(--space-4); } .metric-item { text-align: center; padding: var(--space-3); background-color: var(--bg-secondary); border-radius: var(--radius-md); } .metric-value { font-size: var(--text-lg); font-weight: 600; color: var(--text-primary); margin-bottom: var(--space-1); } .metric-label { font-size: var(--text-xs); color: var(--text-secondary); text-transform: uppercase; letter-spacing: 0.05em; } .chart-container { background-color: var(--bg-card); border-radius: var(--radius-lg); padding: var(--space-6); margin-top: var(--space-6); box-shadow: var(--shadow-sm); } .progress-overlay { position: fixed; top: 0; left: 0; right: 0; bottom: 0; background-color: var(--bg-overlay); z-index: var(--z-modal); display: flex; align-items: center; justify-content: center; opacity: 0; visibility: hidden; transition: all var(--transition-normal); } .progress-overlay.active { opacity: 1; visibility: visible; } .progress-content { background-color: var(--bg-card); border-radius: var(--radius-xl); padding: var(--space-8); text-align: center; max-width: 400px; width: 90%; } @media (max-width: 768px) { .backtesting-header { padding: var(--space-8) 0; } .backtest-form { padding: var(--space-6); } .form-grid { grid-template-columns: 1fr; } .backtest-header { flex-direction: column; gap: var(--space-3); } } </style> {% endblock %} {% block content %} <!-- En-tête de backtesting --> <div class="container"> <div class="backtesting-header"> <div class="text-center"> <h1><i class="fas fa-history"></i> Backtesting de Stratégies</h1> <p class="text-lg opacity-90"> Testez vos stratégies de trading sur des données historiques </p> </div> </div> <!-- Formulaire de création de backtest --> <div class="backtest-form"> <h2 class="mb-6">Nouveau Backtest</h2> <form id="backtestForm" data-validate> <div class="form-grid"> <div class="form-group"> <label class="form-label">Nom du backtest</label> <input type="text" class="form-control" name="name" required placeholder="Mon backtest SPY"> </div> <div class="form-group"> <label class="form-label">Capital initial</label> <input type="number" class="form-control" name="initial_capital" value="100000" min="1000" required> </div> <div class="form-group"> <label class="form-label">Date de début</label> <input type="date" class="form-control" name="start_date" required> </div> <div class="form-group"> <label class="form-label">Date de fin</label> <input type="date" class="form-control" name="end_date" required> </div> </div> <div class="form-group"> <label class="form-label">Symboles à tester</label> <div class="symbol-input"> <input type="text" class="form-control" id="symbolInput" placeholder="SPY, AAPL, TSLA..."> <div class="symbol-tags" id="symbolTags"> <!-- Tags des symboles ajoutés --> </div> </div> </div> <div class="form-grid"> <div class="form-group"> <label class="form-label">Benchmark</label> <select class="form-control form-control form-select" name="benchmark"> <option value="SPY">SPY (S&P 500)</option> <option value="QQQ">QQQ (NASDAQ)</option> <option value="IWM">IWM (Russell 2000)</option> </select> </div> <div class="form-group"> <label class="form-label">Commission (%)</label> <input type="number" class="form-control" name="commission" value="0.1" step="0.01" min="0"> </div> <div class="form-group"> <label class="form-label">Slippage (%)</label> <input type="number" class="form-control" name="slippage" value="0.05" step="0.01" min="0"> </div> </div> <div class="form-group"> <label class="form-label">Description</label> <textarea class="form-control" name="description" rows="3" placeholder="Description de votre stratégie de test..."></textarea> </div> <div class="flex justify-end gap-4"> <button type="button" class="btn btn-secondary" onclick="resetForm()"> <i class="fas fa-undo"></i> Réinitialiser </button> <button type="submit" class="btn btn-primary"> <i class="fas fa-play"></i> Lancer le Backtest </button> </div> </form> </div> <!-- Liste des backtests --> <div class="card"> <div class="card-header"> <h3><i class="fas fa-list"></i> Backtests Récents</h3> </div> <div class="card-body"> <div class="backtest-list" id="backtestList"> <!-- Chargé dynamiquement --> </div> </div> </div> </div> <!-- Overlay de progression --> <div class="progress-overlay" id="progressOverlay"> <div class="progress-content"> <div class="loading-spinner-lg mb-6"></div> <h4 id="progressTitle">Backtest en cours...</h4> <p class="text-secondary mb-6" id="progressMessage">Chargement des données historiques...</p> <div class="progress mb-6"> <div class="progress-bar animated" id="progressBar" style="width: 0%"></div> </div> <div id="progressSteps"> <!-- Étapes de progression --> </div> </div> </div> {% endblock %} {% block extra_scripts %} <script> document.addEventListener('DOMContentLoaded', function() { initializeBacktesting(); loadBacktestList(); // Définir les dates par défaut const endDate = new Date(); const startDate = new Date(); startDate.setFullYear(endDate.getFullYear() - 1); document.querySelector('input[name="end_date"]').value = endDate.toISOString().split('T')[0]; document.querySelector('input[name="start_date"]').value = startDate.toISOString().split('T')[0]; }); function initializeBacktesting() { const symbolInput = document.getElementById('symbolInput'); const symbolTags = document.getElementById('symbolTags'); const form = document.getElementById('backtestForm'); let symbols = []; // Gestion de l'ajout de symboles symbolInput.addEventListener('keypress', function(e) { if (e.key === 'Enter' || e.key === ',') { e.preventDefault(); addSymbol(this.value.trim().toUpperCase()); this.value = ''; } }); symbolInput.addEventListener('blur', function() { if (this.value.trim()) { addSymbol(this.value.trim().toUpperCase()); this.value = ''; } }); function addSymbol(symbol) { if (symbol && !symbols.includes(symbol)) { symbols.push(symbol); updateSymbolTags(); } } function removeSymbol(symbol) { symbols = symbols.filter(s => s !== symbol); updateSymbolTags(); } function updateSymbolTags() { symbolTags.innerHTML = symbols.map(symbol => ` <div class="symbol-tag"> <span>${symbol}</span> <button type="button" onclick="removeSymbol('${symbol}')"> <i class="fas fa-times"></i> </button> </div> `).join(''); } // Rendre les fonctions globales window.removeSymbol = removeSymbol; window.getSymbols = () => symbols; // Gestionnaire de soumission form.addEventListener('submit', handleBacktestSubmit); } async function handleBacktestSubmit(event) { event.preventDefault(); const formData = new FormData(event.target); const symbols = window.getSymbols(); if (symbols.length === 0) { showNotification('Veuillez ajouter au moins un symbole', 'warning'); return; } const backtestData = { name: formData.get('name'), description: formData.get('description'), symbols: symbols, start_date: formData.get('start_date'), end_date: formData.get('end_date'), initial_capital: parseFloat(formData.get('initial_capital')), benchmark: formData.get('benchmark'), commission: parseFloat(formData.get('commission')) / 100, slippage: parseFloat(formData.get('slippage')) / 100, trading_config: {}, risk_config: {} }; try { showProgressOverlay('Création du backtest...'); const response = await fetch('/api/backtesting/create', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(backtestData) }); const result = await response.json(); if (result.success) { // Démarrer le backtest await startBacktest(result.backtest_id); } else { throw new Error(result.error || 'Erreur lors de la création du backtest'); } } catch (error) { hideProgressOverlay(); showNotification('Erreur: ' + error.message, 'error'); } } async function startBacktest(backtestId) { try { updateProgress(10, 'Démarrage du backtest...'); const response = await fetch(`/api/backtesting/${backtestId}/start`, { method: 'POST' }); const result = await response.json(); if (result.success) { trackBacktestProgress(backtestId); } else { throw new Error(result.error || 'Erreur lors du démarrage du backtest'); } } catch (error) { hideProgressOverlay(); showNotification('Erreur: ' + error.message, 'error'); } } function trackBacktestProgress(backtestId) { let progress = 10; const interval = setInterval(async () => { try { const response = await fetch(`/api/backtesting/${backtestId}/status`); const status = await response.json(); if (status.status === 'completed') { clearInterval(interval); updateProgress(100, 'Backtest terminé!'); setTimeout(() => { hideProgressOverlay(); showNotification('Backtest terminé avec succès!', 'success'); loadBacktestList(); resetForm(); }, 1500); } else if (status.status === 'failed') { clearInterval(interval); hideProgressOverlay(); showNotification('Erreur lors du backtest: ' + status.error_message, 'error'); } else if (status.status === 'running') { progress = Math.min(progress + 10, 90); updateProgress(progress, 'Simulation en cours...'); } } catch (error) { clearInterval(interval); hideProgressOverlay(); showNotification('Erreur de suivi: ' + error.message, 'error'); } }, 2000); } function showProgressOverlay(message) { document.getElementById('progressTitle').textContent = 'Backtest en cours...'; document.getElementById('progressMessage').textContent = message; document.getElementById('progressBar').style.width = '0%'; document.getElementById('progressSteps').innerHTML = ''; document.getElementById('progressOverlay').classList.add('active'); } function hideProgressOverlay() { document.getElementById('progressOverlay').classList.remove('active'); } function updateProgress(percentage, message) { document.getElementById('progressBar').style.width = percentage + '%'; document.getElementById('progressMessage').textContent = message; // Ajouter une étape const stepsContainer = document.getElementById('progressSteps'); const step = document.createElement('div'); step.className = 'flex items-center gap-2 mb-4 text-sm'; step.innerHTML = ` <i class="fas fa-check text-success"></i> <span>${message}</span> `; stepsContainer.appendChild(step); } async function loadBacktestList() { try { const response = await fetch('/api/backtesting/list'); const data = await response.json(); const container = document.getElementById('backtestList'); if (data.backtests && data.backtests.length > 0) { container.innerHTML = data.backtests.map(backtest => createBacktestCard(backtest) ).join(''); } else { container.innerHTML = ` <div class="text-center py-12"> <i class="fas fa-history text-4xl text-secondary mb-6"></i> <h4>Aucun backtest</h4> <p class="text-secondary">Créez votre premier backtest ci-dessus</p> </div> `; } } catch (error) { console.error('Erreur chargement backtests:', error); } } function createBacktestCard(backtest) { const statusClass = `status-${backtest.status}`; const date = new Date(backtest.created_at).toLocaleDateString('fr-FR'); let metricsHtml = ''; if (backtest.total_return !== null && backtest.total_return !== undefined) { const returnPercent = (backtest.total_return * 100).toFixed(2); const returnClass = backtest.total_return >= 0 ? 'text-profit' : 'text-loss'; metricsHtml = ` <div class="backtest-metrics"> <div class="metric-item"> <div class="metric-value ${returnClass}">${returnPercent}%</div> <div class="metric-label">Rendement Total</div> </div> </div> `; } return ` <div class="backtest-card" onclick="viewBacktest('${backtest.id}')"> <div class="backtest-header"> <div> <div class="backtest-title">${backtest.name}</div> <div class="backtest-meta"> <i class="fas fa-chart-line"></i> ${backtest.symbols.join(', ')} • <i class="fas fa-calendar"></i> ${date} </div> </div> <div class="backtest-status ${statusClass}"> ${backtest.status} </div> </div> ${metricsHtml} </div> `; } function viewBacktest(backtestId) { // Rediriger vers la page de détails du backtest window.location.href = `/backtesting/${backtestId}`; } function resetForm() { document.getElementById('backtestForm').reset(); document.getElementById('symbolTags').innerHTML = ''; if (window.getSymbols) { window.getSymbols().length = 0; } // Redéfinir les dates par défaut const endDate = new Date(); const startDate = new Date(); startDate.setFullYear(endDate.getFullYear() - 1); document.querySelector('input[name="end_date"]').value = endDate.toISOString().split('T')[0]; document.querySelector('input[name="start_date"]').value = startDate.toISOString().split('T')[0]; } </script> {% endblock %} 