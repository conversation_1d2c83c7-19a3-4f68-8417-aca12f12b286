{% extends "base.html" %}

{% block title %}Configuration - TradingAgents{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- En-tête de configuration -->
        <div class="card mb-4">
            <div class="card-header">
                <h2 class="mb-0">
                    <i class="fas fa-cog me-2"></i>Configuration des Agents de Trading
                </h2>
            </div>
            <div class="card-body">
                <p class="lead">
                    Configurez les paramètres des agents de trading, les modèles LLM et les stratégies d'analyse.
                </p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Configuration des LLM -->
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-brain me-2"></i>Configuration des Modèles LLM
                </h4>
            </div>
            <div class="card-body">
                <form id="llmConfigForm">
                    <div class="mb-3">
                        <label for="llm_provider" class="form-label">Fournisseur LLM</label>
                        <select class="form-select" id="llm_provider" name="llm_provider">
                            <option value="openai">OpenAI</option>
                            <option value="anthropic">Anthropic</option>
                            <option value="google">Google</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="quick_think_llm" class="form-label">Modèle de Réflexion Rapide</label>
                        <select class="form-select" id="quick_think_llm" name="quick_think_llm">
                            <option value="gpt-4o-mini">GPT-4o Mini</option>
                            <option value="gpt-4o">GPT-4o</option>
                            <option value="claude-3-haiku">Claude 3 Haiku</option>
                            <option value="claude-3-sonnet">Claude 3 Sonnet</option>
                            <option value="gemini-1.5-flash">Gemini 1.5 Flash</option>
                        </select>
                        <div class="form-text">Utilisé pour les analyses rapides et les interactions fréquentes</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="deep_think_llm" class="form-label">Modèle de Réflexion Profonde</label>
                        <select class="form-select" id="deep_think_llm" name="deep_think_llm">
                            <option value="o1-preview">O1 Preview</option>
                            <option value="gpt-4o">GPT-4o</option>
                            <option value="claude-3-opus">Claude 3 Opus</option>
                            <option value="claude-3-sonnet">Claude 3 Sonnet</option>
                            <option value="gemini-1.5-pro">Gemini 1.5 Pro</option>
                        </select>
                        <div class="form-text">Utilisé pour les analyses complexes et les décisions critiques</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="backend_url" class="form-label">URL du Backend</label>
                        <input type="url" class="form-control" id="backend_url" name="backend_url" 
                               placeholder="https://api.openai.com/v1">
                        <div class="form-text">URL de l'API du fournisseur LLM</div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Sauvegarder Configuration LLM
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Configuration des agents -->
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-users me-2"></i>Configuration des Agents
                </h4>
            </div>
            <div class="card-body">
                <form id="agentsConfigForm">
                    <div class="mb-3">
                        <label class="form-label">Analystes Actifs</label>
                        <div class="row">
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="market_analyst_config" 
                                           name="selected_analysts" value="market" checked>
                                    <label class="form-check-label" for="market_analyst_config">
                                        <i class="fas fa-chart-line me-1"></i>Analyste Marché
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="social_analyst_config" 
                                           name="selected_analysts" value="social" checked>
                                    <label class="form-check-label" for="social_analyst_config">
                                        <i class="fas fa-users me-1"></i>Analyste Social
                                    </label>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="news_analyst_config" 
                                           name="selected_analysts" value="news" checked>
                                    <label class="form-check-label" for="news_analyst_config">
                                        <i class="fas fa-newspaper me-1"></i>Analyste Actualités
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="fundamentals_analyst_config" 
                                           name="selected_analysts" value="fundamentals" checked>
                                    <label class="form-check-label" for="fundamentals_analyst_config">
                                        <i class="fas fa-calculator me-1"></i>Analyste Fondamental
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="max_debate_rounds" class="form-label">Tours de Débat Maximum</label>
                        <input type="range" class="form-range" id="max_debate_rounds" name="max_debate_rounds" 
                               min="1" max="5" value="2" oninput="updateDebateRoundsValue(this.value)">
                        <div class="d-flex justify-content-between">
                            <small>1 (Rapide)</small>
                            <small id="debateRoundsValue">2</small>
                            <small>5 (Approfondi)</small>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="max_risk_discuss_rounds" class="form-label">Tours de Discussion des Risques</label>
                        <input type="range" class="form-range" id="max_risk_discuss_rounds" name="max_risk_discuss_rounds" 
                               min="1" max="5" value="2" oninput="updateRiskRoundsValue(this.value)">
                        <div class="d-flex justify-content-between">
                            <small>1 (Rapide)</small>
                            <small id="riskRoundsValue">2</small>
                            <small>5 (Approfondi)</small>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="online_tools" name="online_tools" checked>
                            <label class="form-check-label" for="online_tools">
                                Utiliser les Outils en Ligne
                            </label>
                            <div class="form-text">Accès aux données en temps réel vs données mises en cache</div>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Sauvegarder Configuration Agents
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Configuration avancée -->
<div class="row">
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-sliders-h me-2"></i>Configuration Avancée
                </h4>
            </div>
            <div class="card-body">
                <form id="advancedConfigForm">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="temperature" class="form-label">Température du Modèle</label>
                                <input type="range" class="form-range" id="temperature" name="temperature" 
                                       min="0" max="2" step="0.1" value="0.7" oninput="updateTemperatureValue(this.value)">
                                <div class="d-flex justify-content-between">
                                    <small>0 (Déterministe)</small>
                                    <small id="temperatureValue">0.7</small>
                                    <small>2 (Créatif)</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="max_tokens" class="form-label">Tokens Maximum</label>
                                <input type="number" class="form-control" id="max_tokens" name="max_tokens" 
                                       value="4000" min="100" max="8000">
                                <div class="form-text">Limite de tokens par réponse</div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="timeout" class="form-label">Timeout (secondes)</label>
                                <input type="number" class="form-control" id="timeout" name="timeout" 
                                       value="60" min="10" max="300">
                                <div class="form-text">Timeout pour les requêtes API</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="results_dir" class="form-label">Répertoire des Résultats</label>
                                <input type="text" class="form-control" id="results_dir" name="results_dir" 
                                       value="webapp/results" readonly>
                                <div class="form-text">Dossier de sauvegarde des analyses</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="log_level" class="form-label">Niveau de Log</label>
                                <select class="form-select" id="log_level" name="log_level">
                                    <option value="DEBUG">Debug</option>
                                    <option value="INFO" selected>Info</option>
                                    <option value="WARNING">Warning</option>
                                    <option value="ERROR">Error</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Sauvegarder Configuration Avancée
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Préréglages -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-magic me-2"></i>Préréglages de Configuration
                </h4>
            </div>
            <div class="card-body">
                <p class="text-muted mb-3">Chargez rapidement des configurations prédéfinies pour différents scénarios d'utilisation.</p>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <i class="fas fa-bolt fa-2x text-primary mb-2"></i>
                                <h5>Configuration Rapide</h5>
                                <p class="text-muted small">Analyse rapide avec modèles légers</p>
                                <button class="btn btn-outline-primary btn-sm" onclick="loadPreset('fast')">
                                    <i class="fas fa-download me-1"></i>Charger
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <i class="fas fa-balance-scale fa-2x text-success mb-2"></i>
                                <h5>Configuration Équilibrée</h5>
                                <p class="text-muted small">Bon compromis vitesse/qualité</p>
                                <button class="btn btn-outline-success btn-sm" onclick="loadPreset('balanced')">
                                    <i class="fas fa-download me-1"></i>Charger
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card border-warning">
                            <div class="card-body text-center">
                                <i class="fas fa-microscope fa-2x text-warning mb-2"></i>
                                <h5>Configuration Approfondie</h5>
                                <p class="text-muted small">Analyse détaillée avec modèles avancés</p>
                                <button class="btn btn-outline-warning btn-sm" onclick="loadPreset('deep')">
                                    <i class="fas fa-download me-1"></i>Charger
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <hr class="my-4">
                
                <div class="row">
                    <div class="col-md-6">
                        <button class="btn btn-success w-100" onclick="exportConfig()">
                            <i class="fas fa-download me-2"></i>Exporter Configuration Actuelle
                        </button>
                    </div>
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="file" class="form-control" id="configFile" accept=".json">
                            <button class="btn btn-info" onclick="importConfig()">
                                <i class="fas fa-upload me-1"></i>Importer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentConfig = {{ default_config | tojson }};

document.addEventListener('DOMContentLoaded', function() {
    // Charger la configuration actuelle dans les formulaires
    loadCurrentConfig();
    
    // Gérer les soumissions de formulaires
    document.getElementById('llmConfigForm').addEventListener('submit', saveLLMConfig);
    document.getElementById('agentsConfigForm').addEventListener('submit', saveAgentsConfig);
    document.getElementById('advancedConfigForm').addEventListener('submit', saveAdvancedConfig);
});

function loadCurrentConfig() {
    // Charger la configuration LLM
    if (currentConfig.llm_provider) {
        document.getElementById('llm_provider').value = currentConfig.llm_provider;
    }
    if (currentConfig.quick_think_llm) {
        document.getElementById('quick_think_llm').value = currentConfig.quick_think_llm;
    }
    if (currentConfig.deep_think_llm) {
        document.getElementById('deep_think_llm').value = currentConfig.deep_think_llm;
    }
    if (currentConfig.backend_url) {
        document.getElementById('backend_url').value = currentConfig.backend_url;
    }
    
    // Charger la configuration des agents
    if (currentConfig.max_debate_rounds) {
        document.getElementById('max_debate_rounds').value = currentConfig.max_debate_rounds;
        updateDebateRoundsValue(currentConfig.max_debate_rounds);
    }
    if (currentConfig.max_risk_discuss_rounds) {
        document.getElementById('max_risk_discuss_rounds').value = currentConfig.max_risk_discuss_rounds;
        updateRiskRoundsValue(currentConfig.max_risk_discuss_rounds);
    }
    if (currentConfig.online_tools !== undefined) {
        document.getElementById('online_tools').checked = currentConfig.online_tools;
    }
    
    // Charger la configuration avancée
    if (currentConfig.temperature !== undefined) {
        document.getElementById('temperature').value = currentConfig.temperature;
        updateTemperatureValue(currentConfig.temperature);
    }
    if (currentConfig.max_tokens) {
        document.getElementById('max_tokens').value = currentConfig.max_tokens;
    }
    if (currentConfig.timeout) {
        document.getElementById('timeout').value = currentConfig.timeout;
    }
    if (currentConfig.results_dir) {
        document.getElementById('results_dir').value = currentConfig.results_dir;
    }
}

function updateDebateRoundsValue(value) {
    document.getElementById('debateRoundsValue').textContent = value;
}

function updateRiskRoundsValue(value) {
    document.getElementById('riskRoundsValue').textContent = value;
}

function updateTemperatureValue(value) {
    document.getElementById('temperatureValue').textContent = value;
}

function saveLLMConfig(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const config = {
        llm_provider: formData.get('llm_provider'),
        quick_think_llm: formData.get('quick_think_llm'),
        deep_think_llm: formData.get('deep_think_llm'),
        backend_url: formData.get('backend_url')
    };
    
    // Mettre à jour la configuration actuelle
    Object.assign(currentConfig, config);
    
    // Sauvegarder dans le localStorage
    localStorage.setItem('tradingagents_llm_config', JSON.stringify(config));
    
    showAlert('Configuration LLM sauvegardée avec succès!', 'success');
}

function saveAgentsConfig(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    
    // Récupérer les analystes sélectionnés
    const selectedAnalysts = [];
    const analystCheckboxes = e.target.querySelectorAll('input[name="selected_analysts"]:checked');
    analystCheckboxes.forEach(checkbox => {
        selectedAnalysts.push(checkbox.value);
    });
    
    const config = {
        selected_analysts: selectedAnalysts,
        max_debate_rounds: parseInt(formData.get('max_debate_rounds')),
        max_risk_discuss_rounds: parseInt(formData.get('max_risk_discuss_rounds')),
        online_tools: formData.get('online_tools') === 'on'
    };
    
    // Mettre à jour la configuration actuelle
    Object.assign(currentConfig, config);
    
    // Sauvegarder dans le localStorage
    localStorage.setItem('tradingagents_agents_config', JSON.stringify(config));
    
    showAlert('Configuration des agents sauvegardée avec succès!', 'success');
}

function saveAdvancedConfig(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const config = {
        temperature: parseFloat(formData.get('temperature')),
        max_tokens: parseInt(formData.get('max_tokens')),
        timeout: parseInt(formData.get('timeout')),
        results_dir: formData.get('results_dir'),
        log_level: formData.get('log_level')
    };
    
    // Mettre à jour la configuration actuelle
    Object.assign(currentConfig, config);
    
    // Sauvegarder dans le localStorage
    localStorage.setItem('tradingagents_advanced_config', JSON.stringify(config));
    
    showAlert('Configuration avancée sauvegardée avec succès!', 'success');
}

function loadPreset(presetType) {
    let preset = {};
    
    switch(presetType) {
        case 'fast':
            preset = {
                llm_provider: 'openai',
                quick_think_llm: 'gpt-4o-mini',
                deep_think_llm: 'gpt-4o-mini',
                max_debate_rounds: 1,
                max_risk_discuss_rounds: 1,
                temperature: 0.3,
                max_tokens: 2000,
                online_tools: false
            };
            break;
            
        case 'balanced':
            preset = {
                llm_provider: 'openai',
                quick_think_llm: 'gpt-4o-mini',
                deep_think_llm: 'gpt-4o',
                max_debate_rounds: 2,
                max_risk_discuss_rounds: 2,
                temperature: 0.7,
                max_tokens: 4000,
                online_tools: true
            };
            break;
            
        case 'deep':
            preset = {
                llm_provider: 'openai',
                quick_think_llm: 'gpt-4o',
                deep_think_llm: 'o1-preview',
                max_debate_rounds: 3,
                max_risk_discuss_rounds: 3,
                temperature: 0.8,
                max_tokens: 6000,
                online_tools: true
            };
            break;
    }
    
    // Appliquer le préréglage
    Object.assign(currentConfig, preset);
    loadCurrentConfig();
    
    showAlert(`Préréglage "${presetType}" chargé avec succès!`, 'info');
}

function exportConfig() {
    const configStr = JSON.stringify(currentConfig, null, 2);
    const dataBlob = new Blob([configStr], {type: 'application/json'});
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `tradingagents_config_${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    
    showAlert('Configuration exportée avec succès!', 'success');
}

function importConfig() {
    const fileInput = document.getElementById('configFile');
    const file = fileInput.files[0];
    
    if (!file) {
        showAlert('Veuillez sélectionner un fichier de configuration', 'warning');
        return;
    }
    
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const importedConfig = JSON.parse(e.target.result);
            
            // Valider la configuration importée
            if (typeof importedConfig === 'object' && importedConfig !== null) {
                Object.assign(currentConfig, importedConfig);
                loadCurrentConfig();
                showAlert('Configuration importée avec succès!', 'success');
            } else {
                showAlert('Format de fichier de configuration invalide', 'danger');
            }
        } catch (error) {
            showAlert('Erreur lors de la lecture du fichier: ' + error.message, 'danger');
        }
    };
    
    reader.readAsText(file);
}
</script>
{% endblock %}
