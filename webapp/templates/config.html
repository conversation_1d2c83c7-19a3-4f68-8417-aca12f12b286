{% extends "base_modern.html" %} {% block title %}TradingAgents - Configuration{% endblock %} {% block extra_head %} <style> .config-header { background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%); color: var(--text-inverse); padding: var(--space-12) 0; margin-bottom: var(--space-8); border-radius: var(--radius-2xl); position: relative; overflow: hidden; } .config-header::before { content: ''; position: absolute; top: 0; right: 0; width: 200px; height: 200px; background: rgba(255, 255, 255, 0.1); border-radius: 50%; transform: translate(50px, -50px); } .config-section { margin-bottom: var(--space-8); } .preset-card { background-color: var(--bg-card); border: 1px solid var(--border-color); border-radius: var(--radius-lg); padding: var(--space-6); margin-bottom: var(--space-4); transition: all var(--transition-normal); cursor: pointer; } .preset-card:hover { box-shadow: var(--shadow-lg); transform: translateY(-2px); border-color: var(--primary-color); } .preset-card.active { border-color: var(--primary-color); background-color: var(--primary-light); } .preset-title { font-size: var(--text-lg); font-weight: 600; color: var(--text-primary); margin-bottom: var(--space-2); } .preset-description { color: var(--text-secondary); font-size: var(--text-sm); } .config-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: var(--space-8); } .checkbox-card { position: relative; margin-bottom: var(--space-3); } .checkbox-card input[type="checkbox"] { position: absolute; opacity: 0; cursor: pointer; } .checkbox-label { display: flex; align-items: center; gap: var(--space-3); padding: var(--space-4); background-color: var(--bg-secondary); border: 2px solid var(--border-color); border-radius: var(--radius-md); cursor: pointer; transition: all var(--transition-fast); font-weight: 500; } .checkbox-card input[type="checkbox"]:checked + .checkbox-label { background-color: var(--primary-light); border-color: var(--primary-color); color: var(--primary-color); } .checkbox-label:hover { border-color: var(--primary-color); transform: translateY(-1px); } .config-actions { display: flex; gap: var(--space-4); justify-content: flex-end; margin-top: var(--space-8); padding-top: var(--space-6); border-top: 1px solid var(--border-color); } @media (max-width: 768px) { .config-grid { grid-template-columns: 1fr; } .config-header { padding: var(--space-8) 0; } .config-actions { flex-direction: column; } } </style> {% endblock %} {% block content %} <div class="container"> <!-- En-tête de configuration --> <div class="config-header"> <div class="text-center"> <h1><i class="fas fa-cog"></i> Configuration des Agents</h1> <p class="text-lg opacity-90"> Configurez les paramètres des agents de trading, modèles LLM et stratégies d'analyse </p> </div> </div> <!-- Presets de Configuration --> <div class="config-section"> <h2>🎯 Presets de Configuration</h2> <div class="grid grid-cols-1 md:grid-cols-3 gap-6"> <div class="preset-card" data-preset="conservative"> <div class="preset-title"> <i class="fas fa-shield-alt text-success"></i> Conservateur </div> <div class="preset-description"> Configuration sécurisée avec analyse approfondie et risques limités </div> </div> <div class="preset-card" data-preset="balanced"> <div class="preset-title"> <i class="fas fa-balance-scale text-primary"></i> Équilibré </div> <div class="preset-description"> Configuration équilibrée entre performance et sécurité </div> </div> <div class="preset-card" data-preset="aggressive"> <div class="preset-title"> <i class="fas fa-rocket text-warning"></i> Agressif </div> <div class="preset-description"> Configuration optimisée pour la performance maximale </div> </div> </div> </div> <!-- Configuration détaillée --> <div class="config-grid"> <!-- Configuration des LLM --> <div class="card"> <div class="card-header"> <h3><i class="fas fa-brain"></i> Configuration des Modèles LLM</h3> </div> <div class="card-body"> <form id="llmConfigForm" data-validate> <div class="form-group"> <label class="form-label">Fournisseur LLM</label> <select class="form-control form-control form-select" id="llm_provider" name="llm_provider"> <option value="openai">OpenAI</option> <option value="anthropic">Anthropic</option> <option value="google">Google</option> </select> </div> <div class="form-group"> <label class="form-label">Modèle de Réflexion Rapide</label> <select class="form-control form-control form-select" id="quick_think_llm" name="quick_think_llm"> <option value="gpt-4o-mini">GPT-4o Mini</option> <option value="gpt-4o">GPT-4o</option> <option value="claude-3-haiku">Claude 3 Haiku</option> <option value="claude-3-sonnet">Claude 3 Sonnet</option> <option value="gemini-1.5-flash">Gemini 1.5 Flash</option> </select> <div class="text-xs text-secondary mt-2">Utilisé pour les analyses rapides et les interactions fréquentes</div> </div> <div class="form-group"> <label class="form-label">Modèle de Réflexion Profonde</label> <select class="form-control form-control form-select" id="deep_think_llm" name="deep_think_llm"> <option value="o1-preview">O1 Preview</option> <option value="gpt-4o">GPT-4o</option> <option value="claude-3-opus">Claude 3 Opus</option> <option value="claude-3-sonnet">Claude 3 Sonnet</option> <option value="gemini-1.5-pro">Gemini 1.5 Pro</option> </select> <div class="text-xs text-secondary mt-2">Utilisé pour les analyses complexes et les décisions critiques</div> </div> <div class="form-group"> <label class="form-label">URL du Backend</label> <input type="url" class="form-control" id="backend_url" name="backend_url" placeholder="https://api.openai.com/v1"> <div class="text-xs text-secondary mt-2">URL de l'API du fournisseur LLM</div> </div> <button type="submit" class="btn btn-primary"> <i class="fas fa-save"></i> Sauvegarder Configuration LLM </button> </form> </div> </div> </div> <!-- Configuration des agents --> <div class="card"> <div class="card-header"> <h3><i class="fas fa-users"></i> Configuration des Agents</h3> </div> <div class="card-body"> <form id="agentsConfigForm" data-validate> <div class="form-group"> <label class="form-label">Analystes Actifs</label> <div class="grid grid-cols-2 gap-4"> <div class="checkbox-card"> <input type="checkbox" id="market_analyst_config" name="selected_analysts" value="market" checked> <label for="market_analyst_config" class="checkbox-label"> <i class="fas fa-chart-line"></i> Analyste Marché </label> </div> <div class="checkbox-card"> <input type="checkbox" id="social_analyst_config" name="selected_analysts" value="social" checked> <label for="social_analyst_config" class="checkbox-label"> <i class="fas fa-users"></i> Analyste Social </label> </div> <div class="checkbox-card"> <input type="checkbox" id="news_analyst_config" name="selected_analysts" value="news" checked> <label for="news_analyst_config" class="checkbox-label"> <i class="fas fa-newspaper"></i> Analyste Actualités </label> </div> <div class="checkbox-card"> <input type="checkbox" id="fundamentals_analyst_config" name="selected_analysts" value="fundamentals" checked> <label for="fundamentals_analyst_config" class="checkbox-label"> <i class="fas fa-calculator"></i> Analyste Fondamental </label> </div> </div> </div> <div class="mb-4"> <label for="max_debate_rounds" class="form-label">Tours de Débat Maximum</label> <input type="range" class="form-control" id="max_debate_rounds" name="max_debate_rounds" min="1" max="5" value="2" oninput="updateDebateRoundsValue(this.value)"> <div class="flex justify-between"> <small>1 (Rapide)</small> <small id="debateRoundsValue">2</small> <small>5 (Approfondi)</small> </div> </div> <div class="mb-4"> <label for="max_risk_discuss_rounds" class="form-label">Tours de Discussion des Risques</label> <input type="range" class="form-control" id="max_risk_discuss_rounds" name="max_risk_discuss_rounds" min="1" max="5" value="2" oninput="updateRiskRoundsValue(this.value)"> <div class="flex justify-between"> <small>1 (Rapide)</small> <small id="riskRoundsValue">2</small> <small>5 (Approfondi)</small> </div> </div> <div class="mb-4"> <div class="checkbox-card form-switch"> <input class="checkbox-card-input" type="checkbox" id="online_tools" name="online_tools" checked> <label class="checkbox-card-label" for="online_tools"> Utiliser les Outils en Ligne </label> <div class="text-xs text-secondary mt-1">Accès aux données en temps réel vs données mises en cache</div> </div> </div> <button type="submit" class="btn btn-primary"> <i class="fas fa-save mr-2"></i>Sauvegarder Configuration Agents </button> </form> </div> </div> </div> </div> <!-- Configuration avancée --> <div class="grid gap-6"> <div class="col-12"> <div class="card mb-6"> <div class="card-header"> <h3 <i class="fas fa-sliders-h mr-2"></i>Configuration Avancée </h3> </div> <div class="card-body"> <form id="advancedConfigForm"> <div class="grid gap-6"> <div class="md:w-1/3"> <div class="mb-4"> <label for="temperature" class="form-label">Température du Modèle</label> <input type="range" class="form-control" id="temperature" name="temperature" min="0" max="2" step="0.1" value="0.7" oninput="updateTemperatureValue(this.value)"> <div class="flex justify-between"> <small>0 (Déterministe)</small> <small id="temperatureValue">0.7</small> <small>2 (Créatif)</small> </div> </div> </div> <div class="md:w-1/3"> <div class="mb-4"> <label for="max_tokens" class="form-label">Tokens Maximum</label> <input type="number" class="form-control" id="max_tokens" name="max_tokens" value="4000" min="100" max="8000"> <div class="text-xs text-secondary mt-1">Limite de tokens par réponse</div> </div> </div> <div class="md:w-1/3"> <div class="mb-4"> <label for="timeout" class="form-label">Timeout (secondes)</label> <input type="number" class="form-control" id="timeout" name="timeout" value="60" min="10" max="300"> <div class="text-xs text-secondary mt-1">Timeout pour les requêtes API</div> </div> </div> </div> <div class="grid gap-6"> <div class="md:w-1/2"> <div class="mb-4"> <label for="results_dir" class="form-label">Répertoire des Résultats</label> <input type="text" class="form-control" id="results_dir" name="results_dir" value="webapp/results" readonly> <div class="text-xs text-secondary mt-1">Dossier de sauvegarde des analyses</div> </div> </div> <div class="md:w-1/2"> <div class="mb-4"> <label for="log_level" class="form-label">Niveau de Log</label> <select class="form-control form-select" id="log_level" name="log_level"> <option value="DEBUG">Debug</option> <option value="INFO" selected>Info</option> <option value="WARNING">Warning</option> <option value="ERROR">Error</option> </select> </div> </div> </div> <button type="submit" class="btn btn-primary"> <i class="fas fa-save mr-2"></i>Sauvegarder Configuration Avancée </button> </form> </div> </div> </div> </div> <!-- Préréglages --> <div class="grid gap-6"> <div class="col-12"> <div class="card"> <div class="card-header"> <h3 <i class="fas fa-magic mr-2"></i>Préréglages de Configuration </h3> </div> <div class="card-body"> <p class="text-secondary mb-4">Chargez rapidement des configurations prédéfinies pour différents scénarios d'utilisation.</p> <div class="grid gap-6"> <div class="md:w-1/3"> <div class="card border-primary"> <div class="card-body text-center"> <i class="fas fa-bolt fa-2x text-primary mb-4"></i> <h5>Configuration Rapide</h5> <p class="text-secondary small">Analyse rapide avec modèles légers</p> <button class="btn btn btn-secondary btn-sm" onclick="loadPreset('fast')"> <i class="fas fa-download mr-1"></i>Charger </button> </div> </div> </div> <div class="md:w-1/3"> <div class="card border-success"> <div class="card-body text-center"> <i class="fas fa-balance-scale fa-2x text-success mb-4"></i> <h5>Configuration Équilibrée</h5> <p class="text-secondary small">Bon compromis vitesse/qualité</p> <button class="btn btn-outline-success btn-sm" onclick="loadPreset('balanced')"> <i class="fas fa-download mr-1"></i>Charger </button> </div> </div> </div> <div class="md:w-1/3"> <div class="card border-warning"> <div class="card-body text-center"> <i class="fas fa-microscope fa-2x text-warning mb-4"></i> <h5>Configuration Approfondie</h5> <p class="text-secondary small">Analyse détaillée avec modèles avancés</p> <button class="btn btn-outline-warning btn-sm" onclick="loadPreset('deep')"> <i class="fas fa-download mr-1"></i>Charger </button> </div> </div> </div> </div> <hr class="my-4"> <div class="grid gap-6"> <div class="md:w-1/2"> <button class="btn btn-success w-100" onclick="exportConfig()"> <i class="fas fa-download mr-2"></i>Exporter Configuration Actuelle </button> </div> <div class="md:w-1/2"> <div class="input-group"> <input type="file" class="form-control" id="configFile" accept=".json"> <button class="btn btn-info" onclick="importConfig()"> <i class="fas fa-upload mr-1"></i>Importer </button> </div> </div> </div> </div> </div> </div> </div> {% endblock %} {% block extra_js %} <script> let currentConfig = {{ default_config | tojson }}; document.addEventListener('DOMContentLoaded', function() { // Charger la configuration actuelle dans les formulaires loadCurrentConfig(); // Gérer les soumissions de formulaires document.getElementById('llmConfigForm').addEventListener('submit', saveLLMConfig); document.getElementById('agentsConfigForm').addEventListener('submit', saveAgentsConfig); document.getElementById('advancedConfigForm').addEventListener('submit', saveAdvancedConfig); }); function loadCurrentConfig() { // Charger la configuration LLM if (currentConfig.llm_provider) { document.getElementById('llm_provider').value = currentConfig.llm_provider; } if (currentConfig.quick_think_llm) { document.getElementById('quick_think_llm').value = currentConfig.quick_think_llm; } if (currentConfig.deep_think_llm) { document.getElementById('deep_think_llm').value = currentConfig.deep_think_llm; } if (currentConfig.backend_url) { document.getElementById('backend_url').value = currentConfig.backend_url; } // Charger la configuration des agents if (currentConfig.max_debate_rounds) { document.getElementById('max_debate_rounds').value = currentConfig.max_debate_rounds; updateDebateRoundsValue(currentConfig.max_debate_rounds); } if (currentConfig.max_risk_discuss_rounds) { document.getElementById('max_risk_discuss_rounds').value = currentConfig.max_risk_discuss_rounds; updateRiskRoundsValue(currentConfig.max_risk_discuss_rounds); } if (currentConfig.online_tools !== undefined) { document.getElementById('online_tools').checked = currentConfig.online_tools; } // Charger la configuration avancée if (currentConfig.temperature !== undefined) { document.getElementById('temperature').value = currentConfig.temperature; updateTemperatureValue(currentConfig.temperature); } if (currentConfig.max_tokens) { document.getElementById('max_tokens').value = currentConfig.max_tokens; } if (currentConfig.timeout) { document.getElementById('timeout').value = currentConfig.timeout; } if (currentConfig.results_dir) { document.getElementById('results_dir').value = currentConfig.results_dir; } } function updateDebateRoundsValue(value) { document.getElementById('debateRoundsValue').textContent = value; } function updateRiskRoundsValue(value) { document.getElementById('riskRoundsValue').textContent = value; } function updateTemperatureValue(value) { document.getElementById('temperatureValue').textContent = value; } function saveLLMConfig(e) { e.preventDefault(); const formData = new FormData(e.target); const config = { llm_provider: formData.get('llm_provider'), quick_think_llm: formData.get('quick_think_llm'), deep_think_llm: formData.get('deep_think_llm'), backend_url: formData.get('backend_url') }; // Mettre à jour la configuration actuelle Object.assign(currentConfig, config); // Sauvegarder dans le localStorage localStorage.setItem('tradingagents_llm_config', JSON.stringify(config)); showAlert('Configuration LLM sauvegardée avec succès!', 'success'); } function saveAgentsConfig(e) { e.preventDefault(); const formData = new FormData(e.target); // Récupérer les analystes sélectionnés const selectedAnalysts = []; const analystCheckboxes = e.target.querySelectorAll('input[name="selected_analysts"]:checked'); analystCheckboxes.forEach(checkbox => { selectedAnalysts.push(checkbox.value); }); const config = { selected_analysts: selectedAnalysts, max_debate_rounds: parseInt(formData.get('max_debate_rounds')), max_risk_discuss_rounds: parseInt(formData.get('max_risk_discuss_rounds')), online_tools: formData.get('online_tools') === 'on' }; // Mettre à jour la configuration actuelle Object.assign(currentConfig, config); // Sauvegarder dans le localStorage localStorage.setItem('tradingagents_agents_config', JSON.stringify(config)); showAlert('Configuration des agents sauvegardée avec succès!', 'success'); } function saveAdvancedConfig(e) { e.preventDefault(); const formData = new FormData(e.target); const config = { temperature: parseFloat(formData.get('temperature')), max_tokens: parseInt(formData.get('max_tokens')), timeout: parseInt(formData.get('timeout')), results_dir: formData.get('results_dir'), log_level: formData.get('log_level') }; // Mettre à jour la configuration actuelle Object.assign(currentConfig, config); // Sauvegarder dans le localStorage localStorage.setItem('tradingagents_advanced_config', JSON.stringify(config)); showAlert('Configuration avancée sauvegardée avec succès!', 'success'); } function loadPreset(presetType) { let preset = {}; switch(presetType) { case 'fast': preset = { llm_provider: 'openai', quick_think_llm: 'gpt-4o-mini', deep_think_llm: 'gpt-4o-mini', max_debate_rounds: 1, max_risk_discuss_rounds: 1, temperature: 0.3, max_tokens: 2000, online_tools: false }; break; case 'balanced': preset = { llm_provider: 'openai', quick_think_llm: 'gpt-4o-mini', deep_think_llm: 'gpt-4o', max_debate_rounds: 2, max_risk_discuss_rounds: 2, temperature: 0.7, max_tokens: 4000, online_tools: true }; break; case 'deep': preset = { llm_provider: 'openai', quick_think_llm: 'gpt-4o', deep_think_llm: 'o1-preview', max_debate_rounds: 3, max_risk_discuss_rounds: 3, temperature: 0.8, max_tokens: 6000, online_tools: true }; break; } // Appliquer le préréglage Object.assign(currentConfig, preset); loadCurrentConfig(); showAlert(`Préréglage "${presetType}" chargé avec succès!`, 'info'); } function exportConfig() { const configStr = JSON.stringify(currentConfig, null, 2); const dataBlob = new Blob([configStr], {type: 'application/json'}); const link = document.createElement('a'); link.href = URL.createObjectURL(dataBlob); link.download = `tradingagents_config_${new Date().toISOString().split('T')[0]}.json`; link.click(); showAlert('Configuration exportée avec succès!', 'success'); } function importConfig() { const fileInput = document.getElementById('configFile'); const file = fileInput.files[0]; if (!file) { showAlert('Veuillez sélectionner un fichier de configuration', 'warning'); return; } const reader = new FileReader(); reader.onload = function(e) { try { const importedConfig = JSON.parse(e.target.result); // Valider la configuration importée if (typeof importedConfig === 'object' && importedConfig !== null) { Object.assign(currentConfig, importedConfig); loadCurrentConfig(); showAlert('Configuration importée avec succès!', 'success'); } else { showAlert('Format de fichier de configuration invalide', 'danger'); } } catch (error) { showAlert('Erreur lors de la lecture du fichier: ' + error.message, 'danger'); } }; reader.readAsText(file); } </script> {% endblock %}