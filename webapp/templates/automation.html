<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TradingAgents - Automatisation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-running { background-color: #28a745; }
        .status-stopped { background-color: #dc3545; }
        .status-paused { background-color: #ffc107; }
        .status-error { background-color: #fd7e14; }
        
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .alert-card {
            border-left: 4px solid;
            margin-bottom: 10px;
        }
        .alert-info { border-left-color: #17a2b8; }
        .alert-warning { border-left-color: #ffc107; }
        .alert-critical { border-left-color: #dc3545; }
        
        .task-card {
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        .task-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .position-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
        }
        
        .pnl-positive { color: #28a745; font-weight: bold; }
        .pnl-negative { color: #dc3545; font-weight: bold; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-robot"></i> TradingAgents
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">Analyses</a>
                <a class="nav-link active" href="/automation">Automatisation</a>
                <a class="nav-link" href="/backtesting">Backtesting</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Contrôles principaux -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cogs"></i> Contrôle de l'Automatisation</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center mb-3">
                                    <span class="status-indicator" id="automation-status"></span>
                                    <span id="automation-status-text">Chargement...</span>
                                </div>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-success" id="start-automation">
                                        <i class="fas fa-play"></i> Démarrer
                                    </button>
                                    <button class="btn btn-warning" id="pause-automation">
                                        <i class="fas fa-pause"></i> Pause
                                    </button>
                                    <button class="btn btn-danger" id="stop-automation">
                                        <i class="fas fa-stop"></i> Arrêter
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="metric-card">
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <h6>Tâches Actives</h6>
                                            <h4 id="active-tasks">0</h4>
                                        </div>
                                        <div class="col-4">
                                            <h6>Positions</h6>
                                            <h4 id="total-positions">0</h4>
                                        </div>
                                        <div class="col-4">
                                            <h6>P&L Total</h6>
                                            <h4 id="total-pnl">$0.00</h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Tâches d'automatisation -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-tasks"></i> Tâches d'Automatisation</h5>
                        <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#createTaskModal">
                            <i class="fas fa-plus"></i> Nouvelle Tâche
                        </button>
                    </div>
                    <div class="card-body" style="max-height: 500px; overflow-y: auto;">
                        <div id="automation-tasks">
                            <!-- Les tâches seront chargées ici -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Positions surveillées -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-line"></i> Positions Surveillées</h5>
                    </div>
                    <div class="card-body" style="max-height: 500px; overflow-y: auto;">
                        <div id="monitored-positions">
                            <!-- Les positions seront chargées ici -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alertes et notifications -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-bell"></i> Alertes Récentes</h5>
                    </div>
                    <div class="card-body" style="max-height: 300px; overflow-y: auto;">
                        <div id="recent-alerts">
                            <!-- Les alertes seront chargées ici -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour créer une tâche -->
    <div class="modal fade" id="createTaskModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Créer une Tâche d'Automatisation</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createTaskForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Nom de la tâche</label>
                                    <input type="text" class="form-control" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Ticker</label>
                                    <input type="text" class="form-control" name="ticker" placeholder="SPY" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea class="form-control" name="description" rows="2"></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Type de planification</label>
                                    <select class="form-select" name="schedule_type" required>
                                        <option value="daily">Quotidienne</option>
                                        <option value="weekly">Hebdomadaire</option>
                                        <option value="custom">Personnalisée</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Heure d'exécution</label>
                                    <input type="time" class="form-control" name="execution_time" value="09:30">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="auto_execute" id="autoExecute">
                                    <label class="form-check-label" for="autoExecute">
                                        Exécution automatique des ordres
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="enabled" id="taskEnabled" checked>
                                    <label class="form-check-label" for="taskEnabled">
                                        Tâche activée
                                    </label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" id="saveTask">Créer la Tâche</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Variables globales
        let automationStatus = 'stopped';
        
        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            loadAutomationStatus();
            loadAutomationTasks();
            loadMonitoredPositions();
            loadRecentAlerts();
            
            // Actualiser toutes les 30 secondes
            setInterval(() => {
                loadAutomationStatus();
                loadMonitoredPositions();
                loadRecentAlerts();
            }, 30000);
        });
        
        // Charger le statut de l'automatisation
        async function loadAutomationStatus() {
            try {
                const response = await fetch('/api/automation/status');
                const data = await response.json();
                
                const status = data.automation.status;
                const statusIndicator = document.getElementById('automation-status');
                const statusText = document.getElementById('automation-status-text');
                
                statusIndicator.className = `status-indicator status-${status}`;
                statusText.textContent = status === 'running' ? 'En cours' : 
                                       status === 'paused' ? 'En pause' : 'Arrêté';
                
                // Mettre à jour les métriques
                document.getElementById('active-tasks').textContent = data.automation.enabled_tasks;
                document.getElementById('total-positions').textContent = data.monitoring.monitored_positions;
                
                automationStatus = status;
            } catch (error) {
                console.error('Erreur chargement statut:', error);
            }
        }
        
        // Charger les tâches d'automatisation
        async function loadAutomationTasks() {
            try {
                const response = await fetch('/api/automation/tasks');
                const data = await response.json();
                
                const container = document.getElementById('automation-tasks');
                container.innerHTML = '';
                
                if (data.tasks.length === 0) {
                    container.innerHTML = '<p class="text-muted">Aucune tâche configurée</p>';
                    return;
                }
                
                data.tasks.forEach(task => {
                    const taskCard = createTaskCard(task);
                    container.appendChild(taskCard);
                });
            } catch (error) {
                console.error('Erreur chargement tâches:', error);
            }
        }
        
        // Créer une carte de tâche
        function createTaskCard(task) {
            const div = document.createElement('div');
            div.className = 'task-card';
            
            const nextRun = task.next_run ? new Date(task.next_run).toLocaleString() : 'N/A';
            const lastRun = task.last_run ? new Date(task.last_run).toLocaleString() : 'Jamais';
            
            div.innerHTML = `
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="mb-1">${task.name}</h6>
                        <p class="text-muted mb-1">${task.ticker} - ${task.schedule_type}</p>
                        <small class="text-muted">
                            Prochaine: ${nextRun}<br>
                            Dernière: ${lastRun}
                        </small>
                    </div>
                    <div class="text-end">
                        <span class="badge ${task.enabled ? 'bg-success' : 'bg-secondary'}">
                            ${task.enabled ? 'Activée' : 'Désactivée'}
                        </span>
                        <div class="mt-2">
                            <small class="text-success">${task.success_count} succès</small><br>
                            <small class="text-danger">${task.error_count} erreurs</small>
                        </div>
                    </div>
                </div>
            `;
            
            return div;
        }
        
        // Charger les positions surveillées
        async function loadMonitoredPositions() {
            try {
                const response = await fetch('/api/monitoring/positions');
                const data = await response.json();
                
                const container = document.getElementById('monitored-positions');
                container.innerHTML = '';
                
                if (data.positions.length === 0) {
                    container.innerHTML = '<p class="text-muted">Aucune position surveillée</p>';
                    document.getElementById('total-pnl').textContent = '$0.00';
                    return;
                }
                
                let totalPnl = 0;
                data.positions.forEach(position => {
                    totalPnl += position.unrealized_pnl;
                    const positionCard = createPositionCard(position);
                    container.appendChild(positionCard);
                });
                
                // Mettre à jour le P&L total
                const pnlElement = document.getElementById('total-pnl');
                pnlElement.textContent = `$${totalPnl.toFixed(2)}`;
                pnlElement.className = totalPnl >= 0 ? 'pnl-positive' : 'pnl-negative';
                
            } catch (error) {
                console.error('Erreur chargement positions:', error);
            }
        }
        
        // Créer une carte de position
        function createPositionCard(position) {
            const div = document.createElement('div');
            div.className = 'position-card';
            
            const pnlClass = position.unrealized_pnl >= 0 ? 'pnl-positive' : 'pnl-negative';
            const pnlPercent = position.unrealized_pnl_percent.toFixed(2);
            
            div.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">${position.symbol}</h6>
                        <small class="text-muted">
                            ${position.quantity} actions @ $${position.entry_price.toFixed(2)}
                        </small>
                    </div>
                    <div class="text-end">
                        <div class="${pnlClass}">
                            $${position.unrealized_pnl.toFixed(2)}
                        </div>
                        <small class="${pnlClass}">
                            (${pnlPercent}%)
                        </small>
                    </div>
                </div>
            `;
            
            return div;
        }
        
        // Charger les alertes récentes
        async function loadRecentAlerts() {
            try {
                const response = await fetch('/api/monitoring/alerts?limit=10');
                const data = await response.json();
                
                const container = document.getElementById('recent-alerts');
                container.innerHTML = '';
                
                if (data.alerts.length === 0) {
                    container.innerHTML = '<p class="text-muted">Aucune alerte récente</p>';
                    return;
                }
                
                data.alerts.forEach(alert => {
                    const alertCard = createAlertCard(alert);
                    container.appendChild(alertCard);
                });
            } catch (error) {
                console.error('Erreur chargement alertes:', error);
            }
        }
        
        // Créer une carte d'alerte
        function createAlertCard(alert) {
            const div = document.createElement('div');
            div.className = `alert-card alert-${alert.level}`;
            
            const timestamp = new Date(alert.timestamp).toLocaleString();
            
            div.innerHTML = `
                <div class="d-flex justify-content-between align-items-start p-2">
                    <div>
                        <strong>${alert.title}</strong>
                        <p class="mb-1">${alert.message}</p>
                        <small class="text-muted">${timestamp}</small>
                    </div>
                    <span class="badge bg-${alert.level === 'critical' ? 'danger' : alert.level === 'warning' ? 'warning' : 'info'}">
                        ${alert.level.toUpperCase()}
                    </span>
                </div>
            `;
            
            return div;
        }
        
        // Gestionnaires d'événements pour les boutons de contrôle
        document.getElementById('start-automation').addEventListener('click', async () => {
            try {
                const response = await fetch('/api/automation/start', { method: 'POST' });
                if (response.ok) {
                    loadAutomationStatus();
                }
            } catch (error) {
                console.error('Erreur démarrage:', error);
            }
        });
        
        document.getElementById('stop-automation').addEventListener('click', async () => {
            try {
                const response = await fetch('/api/automation/stop', { method: 'POST' });
                if (response.ok) {
                    loadAutomationStatus();
                }
            } catch (error) {
                console.error('Erreur arrêt:', error);
            }
        });
        
        // Gestionnaire pour créer une tâche
        document.getElementById('saveTask').addEventListener('click', async () => {
            try {
                const form = document.getElementById('createTaskForm');
                const formData = new FormData(form);
                
                const taskData = {
                    name: formData.get('name'),
                    description: formData.get('description'),
                    ticker: formData.get('ticker').toUpperCase(),
                    schedule_type: formData.get('schedule_type'),
                    schedule_config: {
                        hour: parseInt(formData.get('execution_time').split(':')[0]),
                        minute: parseInt(formData.get('execution_time').split(':')[1])
                    },
                    trading_config: {
                        auto_execute: formData.get('auto_execute') === 'on'
                    },
                    risk_config: {}
                };
                
                const response = await fetch('/api/automation/tasks', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(taskData)
                });
                
                if (response.ok) {
                    const modal = bootstrap.Modal.getInstance(document.getElementById('createTaskModal'));
                    modal.hide();
                    form.reset();
                    loadAutomationTasks();
                }
            } catch (error) {
                console.error('Erreur création tâche:', error);
            }
        });
    </script>
</body>
</html>
