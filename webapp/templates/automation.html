{% extends "base_modern.html" %} {% block title %}TradingAgents - Automatisation{% endblock %} {% block extra_head %} <style> .automation-header { background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%); color: var(--text-inverse); padding: var(--space-12) 0; margin-bottom: var(--space-8); border-radius: var(--radius-2xl); position: relative; overflow: hidden; } .automation-header::before { content: ''; position: absolute; top: 0; right: 0; width: 200px; height: 200px; background: rgba(255, 255, 255, 0.1); border-radius: 50%; transform: translate(50px, -50px); } .control-panel { background-color: var(--bg-card); border-radius: var(--radius-xl); padding: var(--space-8); box-shadow: var(--shadow-lg); margin-bottom: var(--space-8); } .status-display { display: flex; align-items: center; gap: var(--space-4); margin-bottom: var(--space-6); } .status-indicator { width: 16px; height: 16px; border-radius: 50%; position: relative; } .status-indicator.status-running { background-color: var(--success-color); animation: pulse 2s infinite; } .status-indicator.status-stopped { background-color: var(--error-color); } .status-indicator.status-paused { background-color: var(--warning-color); } .status-text { font-size: var(--text-lg); font-weight: 600; color: var(--text-primary); } .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--space-6); margin-bottom: var(--space-8); } .metric-card { background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%); color: var(--text-inverse); border-radius: var(--radius-xl); padding: var(--space-6); text-align: center; position: relative; overflow: hidden; } .metric-card::before { content: ''; position: absolute; top: 0; right: 0; width: 80px; height: 80px; background: rgba(255, 255, 255, 0.1); border-radius: 50%; transform: translate(20px, -20px); } .metric-value { font-size: var(--text-3xl); font-weight: 700; margin-bottom: var(--space-2); } .metric-label { font-size: var(--text-sm); opacity: 0.9; } .task-card { background-color: var(--bg-card); border: 1px solid var(--border-color); border-radius: var(--radius-lg); padding: var(--space-6); margin-bottom: var(--space-4); transition: all var(--transition-normal); } .task-card:hover { box-shadow: var(--shadow-lg); transform: translateY(-2px); } .task-header { display: flex; justify-content: space-between; align-items: start; margin-bottom: var(--space-4); } .task-title { font-size: var(--text-lg); font-weight: 600; color: var(--text-primary); margin-bottom: var(--space-1); } .task-meta { font-size: var(--text-sm); color: var(--text-secondary); } .task-status { display: flex; flex-direction: column; align-items: end; gap: var(--space-2); } .position-card { background-color: var(--bg-secondary); border-radius: var(--radius-lg); padding: var(--space-4); margin-bottom: var(--space-3); transition: all var(--transition-fast); } .position-card:hover { background-color: var(--bg-tertiary); } .position-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--space-2); } .position-symbol { font-size: var(--text-lg); font-weight: 600; color: var(--text-primary); } .position-pnl { font-weight: 600; font-family: var(--font-mono); } .pnl-positive { color: var(--profit-color); } .pnl-negative { color: var(--loss-color); } .alert-card { border-left: 4px solid; background-color: var(--bg-card); border-radius: var(--radius-md); padding: var(--space-4); margin-bottom: var(--space-3); transition: all var(--transition-fast); } .alert-card:hover { box-shadow: var(--shadow-md); } .alert-info { border-left-color: var(--info-color); } .alert-warning { border-left-color: var(--warning-color); } .alert-critical { border-left-color: var(--error-color); } .alert-header { display: flex; justify-content: space-between; align-items: start; margin-bottom: var(--space-2); } .alert-title { font-weight: 600; color: var(--text-primary); } .alert-time { font-size: var(--text-xs); color: var(--text-secondary); } .control-buttons { display: flex; gap: var(--space-3); flex-wrap: wrap; } @media (max-width: 768px) { .automation-header { padding: var(--space-8) 0; } .control-panel { padding: var(--space-6); } .metrics-grid { grid-template-columns: 1fr; } .control-buttons { justify-content: center; } } </style> {% endblock %} {% block content %} <!-- En-tête d'automatisation --> <div class="container"> <div class="automation-header"> <div class="text-center"> <h1><i class="fas fa-cogs"></i> Automatisation du Trading</h1> <p class="text-lg opacity-90"> Contrôlez et surveillez vos stratégies de trading automatisées </p> </div> </div> <!-- Panneau de contrôle principal --> <div class="control-panel"> <div class="status-display"> <div class="status-indicator status-stopped" id="automation-status"></div> <div class="status-text" id="automation-status-text">Chargement...</div> </div> <div class="control-buttons"> <button class="btn btn-success" id="start-automation" data-tooltip="Démarrer l'automatisation"> <i class="fas fa-play"></i> Démarrer </button> <button class="btn btn-warning" id="pause-automation" data-tooltip="Mettre en pause"> <i class="fas fa-pause"></i> Pause </button> <button class="btn btn-error" id="stop-automation" data-tooltip="Arrêter l'automatisation"> <i class="fas fa-stop"></i> Arrêter </button> <button class="btn btn-secondary" data-bs-toggle="modal" data-bs-target="#createTaskModal" data-tooltip="Créer une nouvelle tâche"> <i class="fas fa-plus"></i> Nouvelle Tâche </button> </div> </div> <!-- Métriques principales --> <div class="metrics-grid"> <div class="metric-card"> <div class="metric-value" id="active-tasks">0</div> <div class="metric-label">Tâches Actives</div> </div> <div class="metric-card"> <div class="metric-value" id="total-positions">0</div> <div class="metric-label">Positions</div> </div> <div class="metric-card"> <div class="metric-value" id="total-pnl">$0.00</div> <div class="metric-label">P&L Total</div> </div> <div class="metric-card"> <div class="metric-value" id="success-rate">0%</div> <div class="metric-label">Taux de Succès</div> </div> </div> <!-- Contenu principal --> <div class="grid grid-cols-1 lg:grid-cols-2 gap-8"> <!-- Tâches d'automatisation --> <div class="card"> <div class="card-header"> <h3><i class="fas fa-tasks"></i> Tâches d'Automatisation</h3> </div> <div class="card-body" style="max-height: 600px; overflow-y: auto;"> <div id="automation-tasks"> <!-- Les tâches seront chargées ici --> </div> </div> </div> <!-- Positions surveillées --> <div class="card"> <div class="card-header"> <h3><i class="fas fa-chart-line"></i> Positions Surveillées</h3> </div> <div class="card-body" style="max-height: 600px; overflow-y: auto;"> <div id="monitored-positions"> <!-- Les positions seront chargées ici --> </div> </div> </div> </div> <!-- Alertes et notifications --> <div class="card mt-8"> <div class="card-header"> <h3><i class="fas fa-bell"></i> Alertes Récentes</h3> </div> <div class="card-body" style="max-height: 400px; overflow-y: auto;"> <div id="recent-alerts"> <!-- Les alertes seront chargées ici --> </div> </div> </div> </div> <!-- Modal pour créer une tâche --> <div class="modal fade" id="createTaskModal" tabindex="-1"> <div class="modal-dialog modal-lg"> <div class="modal-content"> <div class="modal-header"> <h5 class="modal-title">Créer une Tâche d'Automatisation</h5> <button type="button" class="btn-close" data-bs-dismiss="modal"></button> </div> <div class="modal-body"> <form id="createTaskForm"> <div class="grid gap-6"> <div class="md:w-1/2"> <div class="mb-4"> <label class="form-label">Nom de la tâche</label> <input type="text" class="form-control" name="name" required> </div> </div> <div class="md:w-1/2"> <div class="mb-4"> <label class="form-label">Ticker</label> <input type="text" class="form-control" name="ticker" placeholder="SPY" required> </div> </div> </div> <div class="mb-4"> <label class="form-label">Description</label> <textarea class="form-control" name="description" rows="2"></textarea> </div> <div class="grid gap-6"> <div class="md:w-1/2"> <div class="mb-4"> <label class="form-label">Type de planification</label> <select class="form-control form-select" name="schedule_type" required> <option value="daily">Quotidienne</option> <option value="weekly">Hebdomadaire</option> <option value="custom">Personnalisée</option> </select> </div> </div> <div class="md:w-1/2"> <div class="mb-4"> <label class="form-label">Heure d'exécution</label> <input type="time" class="form-control" name="execution_time" value="09:30"> </div> </div> </div> <div class="grid gap-6"> <div class="md:w-1/2"> <div class="checkbox-card"> <input class="checkbox-card-input" type="checkbox" name="auto_execute" id="autoExecute"> <label class="checkbox-card-label" for="autoExecute"> Exécution automatique des ordres </label> </div> </div> <div class="md:w-1/2"> <div class="checkbox-card"> <input class="checkbox-card-input" type="checkbox" name="enabled" id="taskEnabled" checked> <label class="checkbox-card-label" for="taskEnabled"> Tâche activée </label> </div> </div> </div> </form> </div> <div class="modal-footer"> <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button> <button type="button" class="btn btn-primary" id="saveTask">Créer la Tâche</button> </div> </div> </div> </div> <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script> <script> // Variables globales let automationStatus = 'stopped'; // Initialisation document.addEventListener('DOMContentLoaded', function() { loadAutomationStatus(); loadAutomationTasks(); loadMonitoredPositions(); loadRecentAlerts(); // Actualiser toutes les 30 secondes setInterval(() => { loadAutomationStatus(); loadMonitoredPositions(); loadRecentAlerts(); }, 30000); }); // Charger le statut de l'automatisation async function loadAutomationStatus() { try { const response = await fetch('/api/automation/status'); const data = await response.json(); const status = data.automation.status; const statusIndicator = document.getElementById('automation-status'); const statusText = document.getElementById('automation-status-text'); statusIndicator.className = `status-indicator status-${status}`; statusText.textContent = status === 'running' ? 'En cours' : status === 'paused' ? 'En pause' : 'Arrêté'; // Mettre à jour les métriques document.getElementById('active-tasks').textContent = data.automation.enabled_tasks; document.getElementById('total-positions').textContent = data.monitoring.monitored_positions; automationStatus = status; } catch (error) { console.error('Erreur chargement statut:', error); } } // Charger les tâches d'automatisation async function loadAutomationTasks() { try { const response = await fetch('/api/automation/tasks'); const data = await response.json(); const container = document.getElementById('automation-tasks'); container.innerHTML = ''; if (data.tasks.length === 0) { container.innerHTML = '<p class="text-secondary">Aucune tâche configurée</p>'; return; } data.tasks.forEach(task => { const taskCard = createTaskCard(task); container.appendChild(taskCard); }); } catch (error) { console.error('Erreur chargement tâches:', error); } } // Créer une carte de tâche function createTaskCard(task) { const div = document.createElement('div'); div.className = 'task-card'; const nextRun = task.next_run ? new Date(task.next_run).toLocaleString('fr-FR') : 'N/A'; const lastRun = task.last_run ? new Date(task.last_run).toLocaleString('fr-FR') : 'Jamais'; div.innerHTML = ` <div class="task-header"> <div> <div class="task-title">${task.name}</div> <div class="task-meta"> <i class="fas fa-chart-line"></i> ${task.ticker} • <i class="fas fa-clock"></i> ${task.schedule_type} </div> <div class="text-xs text-secondary mt-3"> <div><i class="fas fa-arrow-right"></i> Prochaine: ${nextRun}</div> <div><i class="fas fa-history"></i> Dernière: ${lastRun}</div> </div> </div> <div class="task-status"> <span class="badge ${task.enabled ? 'badge-success' : 'badge-secondary'}"> ${task.enabled ? 'Activée' : 'Désactivée'} </span> <div class="text-xs text-right"> <div class="text-success">${task.success_count} succès</div> <div class="text-error">${task.error_count} erreurs</div> </div> </div> </div> `; return div; } // Charger les positions surveillées async function loadMonitoredPositions() { try { const response = await fetch('/api/monitoring/positions'); const data = await response.json(); const container = document.getElementById('monitored-positions'); container.innerHTML = ''; if (data.positions.length === 0) { container.innerHTML = '<p class="text-secondary">Aucune position surveillée</p>'; document.getElementById('total-pnl').textContent = '$0.00'; return; } let totalPnl = 0; data.positions.forEach(position => { totalPnl += position.unrealized_pnl; const positionCard = createPositionCard(position); container.appendChild(positionCard); }); // Mettre à jour le P&L total const pnlElement = document.getElementById('total-pnl'); pnlElement.textContent = `$${totalPnl.toFixed(2)}`; pnlElement.className = totalPnl >= 0 ? 'pnl-positive' : 'pnl-negative'; } catch (error) { console.error('Erreur chargement positions:', error); } } // Créer une carte de position function createPositionCard(position) { const div = document.createElement('div'); div.className = 'position-card'; const pnlClass = position.unrealized_pnl >= 0 ? 'pnl-positive' : 'pnl-negative'; const pnlPercent = position.unrealized_pnl_percent.toFixed(2); const pnlIcon = position.unrealized_pnl >= 0 ? 'fa-arrow-up' : 'fa-arrow-down'; div.innerHTML = ` <div class="position-header"> <div class="position-symbol">${position.symbol}</div> <div class="position-pnl ${pnlClass}"> <i class="fas ${pnlIcon}"></i> $${position.unrealized_pnl.toFixed(2)} </div> </div> <div class="flex justify-between items-center text-sm"> <div class="text-secondary"> ${position.quantity} actions @ $${position.entry_price.toFixed(2)} </div> <div class="${pnlClass}"> ${pnlPercent}% </div> </div> `; return div; } // Charger les alertes récentes async function loadRecentAlerts() { try { const response = await fetch('/api/monitoring/alerts?limit=10'); const data = await response.json(); const container = document.getElementById('recent-alerts'); container.innerHTML = ''; if (data.alerts.length === 0) { container.innerHTML = '<p class="text-secondary">Aucune alerte récente</p>'; return; } data.alerts.forEach(alert => { const alertCard = createAlertCard(alert); container.appendChild(alertCard); }); } catch (error) { console.error('Erreur chargement alertes:', error); } } // Créer une carte d'alerte function createAlertCard(alert) { const div = document.createElement('div'); div.className = `alert-card alert-${alert.level}`; const timestamp = new Date(alert.timestamp).toLocaleString('fr-FR'); const levelIcons = { 'info': 'fa-info-circle', 'warning': 'fa-exclamation-triangle', 'critical': 'fa-exclamation-circle' }; div.innerHTML = ` <div class="alert-header"> <div> <div class="alert-title"> <i class="fas ${levelIcons[alert.level] || 'fa-info-circle'}"></i> ${alert.title} </div> <p class="text-secondary mb-4">${alert.message}</p> <div class="alert-time">${timestamp}</div> </div> <span class="badge badge-${alert.level === 'critical' ? 'error' : alert.level === 'warning' ? 'warning' : 'info'}"> ${alert.level.toUpperCase()} </span> </div> `; return div; } // Gestionnaires d'événements pour les boutons de contrôle document.getElementById('start-automation').addEventListener('click', async () => { try { const response = await fetch('/api/automation/start', { method: 'POST' }); if (response.ok) { loadAutomationStatus(); } } catch (error) { console.error('Erreur démarrage:', error); } }); document.getElementById('stop-automation').addEventListener('click', async () => { try { const response = await fetch('/api/automation/stop', { method: 'POST' }); if (response.ok) { loadAutomationStatus(); } } catch (error) { console.error('Erreur arrêt:', error); } }); // Gestionnaire pour créer une tâche document.getElementById('saveTask').addEventListener('click', async () => { try { const form = document.getElementById('createTaskForm'); const formData = new FormData(form); const taskData = { name: formData.get('name'), description: formData.get('description'), ticker: formData.get('ticker').toUpperCase(), schedule_type: formData.get('schedule_type'), schedule_config: { hour: parseInt(formData.get('execution_time').split(':')[0]), minute: parseInt(formData.get('execution_time').split(':')[1]) }, trading_config: { auto_execute: formData.get('auto_execute') === 'on' }, risk_config: {} }; const response = await fetch('/api/automation/tasks', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(taskData) }); if (response.ok) { const modal = bootstrap.Modal.getInstance(document.getElementById('createTaskModal')); modal.hide(); form.reset(); loadAutomationTasks(); } } catch (error) { console.error('Erreur création tâche:', error); } }); </script> {% endblock %} 