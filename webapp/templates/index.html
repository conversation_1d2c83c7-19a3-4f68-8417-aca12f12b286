{% extends "base.html" %}

{% block title %}Accueil - TradingAgents{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- En-tête de bienvenue -->
        <div class="card mb-4">
            <div class="card-header">
                <h2 class="mb-0">
                    <i class="fas fa-robot me-2"></i>Bienvenue dans TradingAgents Web Interface
                </h2>
            </div>
            <div class="card-body">
                <p class="lead">
                    Interface web pour contrôler et visualiser les agents de trading multi-agents alimentés par LLM.
                </p>
                <p>
                    Cette plateforme vous permet de déployer des équipes d'agents spécialisés pour analyser les marchés financiers,
                    prendre des décisions de trading éclairées et gérer les risques de manière collaborative.
                </p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Formulaire de nouvelle analyse -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-play-circle me-2"></i>Nouvelle Analyse
                </h4>
            </div>
            <div class="card-body">
                <form id="analysisForm">
                    <div class="mb-3">
                        <label for="ticker" class="form-label">Symbole du Ticker</label>
                        <input type="text" class="form-control" id="ticker" name="ticker" 
                               placeholder="Ex: AAPL, NVDA, SPY" value="SPY" required>
                        <div class="form-text">Entrez le symbole du ticker à analyser</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="trade_date" class="form-label">Date d'Analyse</label>
                        <input type="date" class="form-control" id="trade_date" name="trade_date" required>
                        <div class="form-text">Date pour laquelle effectuer l'analyse</div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Analystes Sélectionnés</label>
                        <div class="row">
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="market_analyst" 
                                           name="analysts" value="market" checked>
                                    <label class="form-check-label" for="market_analyst">
                                        <i class="fas fa-chart-line me-1"></i>Analyste Marché
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="social_analyst" 
                                           name="analysts" value="social" checked>
                                    <label class="form-check-label" for="social_analyst">
                                        <i class="fas fa-users me-1"></i>Analyste Social
                                    </label>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="news_analyst" 
                                           name="analysts" value="news" checked>
                                    <label class="form-check-label" for="news_analyst">
                                        <i class="fas fa-newspaper me-1"></i>Analyste Actualités
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="fundamentals_analyst" 
                                           name="analysts" value="fundamentals" checked>
                                    <label class="form-check-label" for="fundamentals_analyst">
                                        <i class="fas fa-calculator me-1"></i>Analyste Fondamental
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="research_depth" class="form-label">Profondeur de Recherche</label>
                        <select class="form-select" id="research_depth" name="research_depth">
                            <option value="1">Rapide (1 tour de débat)</option>
                            <option value="2" selected>Standard (2 tours de débat)</option>
                            <option value="3">Approfondi (3 tours de débat)</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100" id="startAnalysisBtn">
                        <i class="fas fa-rocket me-2"></i>Démarrer l'Analyse
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Statut des agents -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-heartbeat me-2"></i>Statut des Agents
                </h4>
            </div>
            <div class="card-body">
                <div id="agentsStatus">
                    <!-- Équipe d'Analystes -->
                    <div class="agent-card">
                        <h6 class="fw-bold mb-2">
                            <i class="fas fa-chart-bar me-2"></i>Équipe d'Analystes
                        </h6>
                        <div class="row">
                            <div class="col-6">
                                <div class="d-flex align-items-center mb-1">
                                    <span class="status-indicator status-pending" id="market-status"></span>
                                    <small>Analyste Marché</small>
                                </div>
                                <div class="d-flex align-items-center mb-1">
                                    <span class="status-indicator status-pending" id="social-status"></span>
                                    <small>Analyste Social</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="d-flex align-items-center mb-1">
                                    <span class="status-indicator status-pending" id="news-status"></span>
                                    <small>Analyste Actualités</small>
                                </div>
                                <div class="d-flex align-items-center mb-1">
                                    <span class="status-indicator status-pending" id="fundamentals-status"></span>
                                    <small>Analyste Fondamental</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Équipe de Recherche -->
                    <div class="agent-card">
                        <h6 class="fw-bold mb-2">
                            <i class="fas fa-search me-2"></i>Équipe de Recherche
                        </h6>
                        <div class="row">
                            <div class="col-6">
                                <div class="d-flex align-items-center mb-1">
                                    <span class="status-indicator status-pending" id="bull-status"></span>
                                    <small>Chercheur Haussier</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="d-flex align-items-center mb-1">
                                    <span class="status-indicator status-pending" id="bear-status"></span>
                                    <small>Chercheur Baissier</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Équipe de Trading -->
                    <div class="agent-card">
                        <h6 class="fw-bold mb-2">
                            <i class="fas fa-exchange-alt me-2"></i>Équipe de Trading
                        </h6>
                        <div class="d-flex align-items-center mb-1">
                            <span class="status-indicator status-pending" id="trader-status"></span>
                            <small>Trader Principal</small>
                        </div>
                    </div>
                    
                    <!-- Gestion des Risques -->
                    <div class="agent-card">
                        <h6 class="fw-bold mb-2">
                            <i class="fas fa-shield-alt me-2"></i>Gestion des Risques
                        </h6>
                        <div class="row">
                            <div class="col-4">
                                <div class="d-flex align-items-center mb-1">
                                    <span class="status-indicator status-pending" id="risky-status"></span>
                                    <small>Analyste Agressif</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="d-flex align-items-center mb-1">
                                    <span class="status-indicator status-pending" id="neutral-status"></span>
                                    <small>Analyste Neutre</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="d-flex align-items-center mb-1">
                                    <span class="status-indicator status-pending" id="safe-status"></span>
                                    <small>Analyste Conservateur</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Résultats en temps réel -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card" id="resultsCard" style="display: none;">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>Résultats de l'Analyse
                </h4>
            </div>
            <div class="card-body">
                <div id="analysisProgress" class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Progression de l'analyse</span>
                        <span id="progressText">0%</span>
                    </div>
                    <div class="progress progress-custom">
                        <div class="progress-bar progress-bar-custom" id="progressBar" 
                             role="progressbar" style="width: 0%"></div>
                    </div>
                </div>
                
                <div id="analysisResults">
                    <!-- Les résultats seront affichés ici -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Analyses récentes -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-history me-2"></i>Analyses Récentes
                </h4>
            </div>
            <div class="card-body">
                <div id="recentAnalyses">
                    <p class="text-muted">Chargement des analyses récentes...</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Définir la date par défaut à aujourd'hui
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('trade_date').value = today;
    
    // Charger les analyses récentes
    loadRecentAnalyses();
    
    // Gérer la soumission du formulaire
    document.getElementById('analysisForm').addEventListener('submit', function(e) {
        e.preventDefault();
        startNewAnalysis();
    });
    
    // Écouter les événements WebSocket
    socket.on('analysis_status', function(data) {
        handleAnalysisStatus(data);
    });
    
    socket.on('analysis_complete', function(data) {
        handleAnalysisComplete(data);
    });
    
    socket.on('analysis_error', function(data) {
        handleAnalysisError(data);
    });
});

function startNewAnalysis() {
    const form = document.getElementById('analysisForm');
    const formData = new FormData(form);
    
    // Récupérer les analystes sélectionnés
    const selectedAnalysts = [];
    const analystCheckboxes = form.querySelectorAll('input[name="analysts"]:checked');
    analystCheckboxes.forEach(checkbox => {
        selectedAnalysts.push(checkbox.value);
    });
    
    const data = {
        ticker: formData.get('ticker'),
        trade_date: formData.get('trade_date'),
        config: {
            selected_analysts: selectedAnalysts,
            max_debate_rounds: parseInt(formData.get('research_depth')),
            max_risk_discuss_rounds: parseInt(formData.get('research_depth'))
        }
    };
    
    // Désactiver le bouton et afficher le spinner
    const btn = document.getElementById('startAnalysisBtn');
    btn.disabled = true;
    btn.innerHTML = '<span class="loading-spinner me-2"></span>Analyse en cours...';
    
    // Afficher la carte de résultats
    document.getElementById('resultsCard').style.display = 'block';
    
    // Envoyer la requête
    fetch('/api/start_analysis', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showAlert('Analyse démarrée avec succès!', 'success');
            window.currentSessionId = result.session_id;
        } else {
            showAlert('Erreur: ' + result.error, 'danger');
            resetAnalysisButton();
        }
    })
    .catch(error => {
        showAlert('Erreur de connexion: ' + error.message, 'danger');
        resetAnalysisButton();
    });
}

function resetAnalysisButton() {
    const btn = document.getElementById('startAnalysisBtn');
    btn.disabled = false;
    btn.innerHTML = '<i class="fas fa-rocket me-2"></i>Démarrer l\'Analyse';
}

function handleAnalysisStatus(data) {
    console.log('Statut d\'analyse:', data);
    updateProgress(25); // Exemple de progression
}

function handleAnalysisComplete(data) {
    console.log('Analyse terminée:', data);
    updateProgress(100);
    displayResults(data.result);
    resetAnalysisButton();
    loadRecentAnalyses(); // Recharger les analyses récentes
}

function handleAnalysisError(data) {
    console.log('Erreur d\'analyse:', data);
    showAlert('Erreur lors de l\'analyse: ' + data.error, 'danger');
    resetAnalysisButton();
}

function updateProgress(percentage) {
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    
    progressBar.style.width = percentage + '%';
    progressText.textContent = percentage + '%';
}

function displayResults(result) {
    const resultsDiv = document.getElementById('analysisResults');
    
    resultsDiv.innerHTML = `
        <div class="alert alert-success">
            <h5><i class="fas fa-check-circle me-2"></i>Analyse Terminée</h5>
            <p><strong>Ticker:</strong> ${result.ticker}</p>
            <p><strong>Date:</strong> ${result.trade_date}</p>
            <p><strong>Décision:</strong> <span class="badge bg-primary">${result.decision}</span></p>
            <p><strong>Session ID:</strong> ${result.session_id}</p>
        </div>
        <div class="text-center">
            <a href="/dashboard" class="btn btn-outline-primary">
                <i class="fas fa-chart-line me-2"></i>Voir les Détails
            </a>
        </div>
    `;
}

function loadRecentAnalyses() {
    fetch('/api/list_results')
    .then(response => response.json())
    .then(analyses => {
        displayRecentAnalyses(analyses);
    })
    .catch(error => {
        console.error('Erreur lors du chargement des analyses:', error);
        document.getElementById('recentAnalyses').innerHTML = 
            '<p class="text-danger">Erreur lors du chargement des analyses récentes.</p>';
    });
}

function displayRecentAnalyses(analyses) {
    const container = document.getElementById('recentAnalyses');
    
    if (analyses.length === 0) {
        container.innerHTML = '<p class="text-muted">Aucune analyse récente trouvée.</p>';
        return;
    }
    
    const recentAnalyses = analyses.slice(-5).reverse(); // 5 plus récentes
    
    let html = '<div class="table-responsive"><table class="table table-hover">';
    html += '<thead><tr><th>Ticker</th><th>Date</th><th>Décision</th><th>Timestamp</th><th>Actions</th></tr></thead><tbody>';
    
    recentAnalyses.forEach(analysis => {
        html += `
            <tr>
                <td><strong>${analysis.ticker}</strong></td>
                <td>${analysis.trade_date}</td>
                <td><span class="badge bg-secondary">${analysis.decision || 'N/A'}</span></td>
                <td>${new Date(analysis.timestamp).toLocaleString('fr-FR')}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewAnalysis('${analysis.session_id}')">
                        <i class="fas fa-eye"></i> Voir
                    </button>
                </td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    container.innerHTML = html;
}

function viewAnalysis(sessionId) {
    // Rediriger vers le tableau de bord avec l'ID de session
    window.location.href = `/dashboard?session=${sessionId}`;
}
</script>
{% endblock %}
