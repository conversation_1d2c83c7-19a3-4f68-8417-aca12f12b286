{% extends "base_modern.html" %}

{% block title %}TradingAgents - Démonstration Interface Moderne{% endblock %}

{% block extra_head %}
<style>
    .demo-section {
        margin-bottom: var(--space-12);
    }
    
    .demo-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
        color: var(--text-inverse);
        padding: var(--space-12) 0;
        margin-bottom: var(--space-8);
        border-radius: var(--radius-2xl);
        text-align: center;
    }
    
    .component-showcase {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--space-6);
        margin-bottom: var(--space-8);
    }
    
    .demo-card {
        background-color: var(--bg-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--space-6);
        transition: all var(--transition-normal);
    }
    
    .demo-card:hover {
        box-shadow: var(--shadow-lg);
        transform: translateY(-2px);
    }
    
    .demo-title {
        font-size: var(--text-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--space-4);
        display: flex;
        align-items: center;
        gap: var(--space-2);
    }
    
    .color-palette {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: var(--space-3);
    }
    
    .color-item {
        text-align: center;
        padding: var(--space-4);
        border-radius: var(--radius-md);
        color: white;
        font-size: var(--text-sm);
        font-weight: 500;
    }
    
    .button-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: var(--space-3);
    }
    
    .form-demo {
        display: grid;
        gap: var(--space-4);
    }
    
    .chart-demo {
        height: 300px;
        background-color: var(--bg-secondary);
        border-radius: var(--radius-md);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text-muted);
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <!-- En-tête de démonstration -->
    <div class="demo-header">
        <h1><i class="fas fa-palette"></i> Interface Moderne TradingAgents</h1>
        <p class="text-lg opacity-90">
            Démonstration des composants et fonctionnalités de l'interface modernisée
        </p>
    </div>
    
    <!-- Palette de couleurs -->
    <div class="demo-section">
        <h2>🎨 Palette de Couleurs</h2>
        <div class="demo-card">
            <div class="demo-title">
                <i class="fas fa-palette"></i>
                Couleurs du Système
            </div>
            <div class="color-palette">
                <div class="color-item" style="background-color: var(--primary-color);">Primary</div>
                <div class="color-item" style="background-color: var(--accent-color);">Accent</div>
                <div class="color-item" style="background-color: var(--success-color);">Success</div>
                <div class="color-item" style="background-color: var(--warning-color);">Warning</div>
                <div class="color-item" style="background-color: var(--error-color);">Error</div>
                <div class="color-item" style="background-color: var(--info-color);">Info</div>
                <div class="color-item" style="background-color: var(--profit-color);">Profit</div>
                <div class="color-item" style="background-color: var(--loss-color);">Loss</div>
            </div>
        </div>
    </div>
    
    <!-- Composants UI -->
    <div class="demo-section">
        <h2>🧩 Composants UI</h2>
        <div class="component-showcase">
            <!-- Boutons -->
            <div class="demo-card">
                <div class="demo-title">
                    <i class="fas fa-mouse-pointer"></i>
                    Boutons
                </div>
                <div class="button-grid">
                    <button class="btn btn-primary">Primary</button>
                    <button class="btn btn-secondary">Secondary</button>
                    <button class="btn btn-success">Success</button>
                    <button class="btn btn-warning">Warning</button>
                    <button class="btn btn-error">Error</button>
                    <button class="btn btn-primary btn-sm">Small</button>
                    <button class="btn btn-primary btn-lg">Large</button>
                    <button class="btn btn-primary" disabled>Disabled</button>
                </div>
            </div>
            
            <!-- Badges -->
            <div class="demo-card">
                <div class="demo-title">
                    <i class="fas fa-tags"></i>
                    Badges
                </div>
                <div class="flex flex-wrap gap-3">
                    <span class="badge badge-primary">Primary</span>
                    <span class="badge badge-success">Success</span>
                    <span class="badge badge-warning">Warning</span>
                    <span class="badge badge-error">Error</span>
                    <span class="badge badge-secondary">Secondary</span>
                </div>
            </div>
            
            <!-- Alertes -->
            <div class="demo-card">
                <div class="demo-title">
                    <i class="fas fa-exclamation-triangle"></i>
                    Alertes
                </div>
                <div class="alert alert-info">
                    <strong>Info:</strong> Ceci est une alerte d'information.
                </div>
                <div class="alert alert-success">
                    <strong>Succès:</strong> Opération réussie!
                </div>
                <div class="alert alert-warning">
                    <strong>Attention:</strong> Vérifiez vos paramètres.
                </div>
                <div class="alert alert-error">
                    <strong>Erreur:</strong> Une erreur s'est produite.
                </div>
            </div>
            
            <!-- Formulaires -->
            <div class="demo-card">
                <div class="demo-title">
                    <i class="fas fa-edit"></i>
                    Formulaires
                </div>
                <div class="form-demo">
                    <div class="form-group">
                        <label class="form-label">Champ de texte</label>
                        <input type="text" class="form-control" placeholder="Entrez du texte...">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Sélection</label>
                        <select class="form-control form-select">
                            <option>Option 1</option>
                            <option>Option 2</option>
                            <option>Option 3</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Zone de texte</label>
                        <textarea class="form-control" rows="3" placeholder="Entrez votre message..."></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Métriques de trading -->
    <div class="demo-section">
        <h2>📊 Métriques de Trading</h2>
        <div class="component-showcase">
            <div class="metric-card">
                <div class="metric-value">+12.5%</div>
                <div class="metric-label">Rendement Total</div>
                <div class="metric-change positive">
                    <i class="fas fa-arrow-up"></i>
                    +2.3% cette semaine
                </div>
            </div>
            <div class="metric-card">
                <div class="metric-value">$125,430</div>
                <div class="metric-label">Valeur du Portefeuille</div>
                <div class="metric-change positive">
                    <i class="fas fa-arrow-up"></i>
                    +$3,250 aujourd'hui
                </div>
            </div>
            <div class="metric-card">
                <div class="metric-value">1.85</div>
                <div class="metric-label">Ratio de Sharpe</div>
                <div class="metric-change neutral">
                    <i class="fas fa-minus"></i>
                    Stable
                </div>
            </div>
            <div class="metric-card">
                <div class="metric-value">-5.2%</div>
                <div class="metric-label">Drawdown Max</div>
                <div class="metric-change negative">
                    <i class="fas fa-arrow-down"></i>
                    Risque modéré
                </div>
            </div>
        </div>
    </div>
    
    <!-- Graphiques -->
    <div class="demo-section">
        <h2>📈 Graphiques</h2>
        <div class="component-showcase">
            <div class="demo-card">
                <div class="demo-title">
                    <i class="fas fa-chart-line"></i>
                    Graphique de Prix
                </div>
                <div class="chart-demo">
                    <canvas id="priceChart"></canvas>
                </div>
            </div>
            <div class="demo-card">
                <div class="demo-title">
                    <i class="fas fa-chart-pie"></i>
                    Répartition du Portefeuille
                </div>
                <div class="chart-demo">
                    <canvas id="allocationChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Fonctionnalités interactives -->
    <div class="demo-section">
        <h2>⚡ Fonctionnalités Interactives</h2>
        <div class="component-showcase">
            <div class="demo-card">
                <div class="demo-title">
                    <i class="fas fa-magic"></i>
                    Actions Rapides
                </div>
                <div class="button-grid">
                    <button class="btn btn-primary" onclick="showNotification('Notification de test!', 'info')">
                        <i class="fas fa-bell"></i>
                        Notification
                    </button>
                    <button class="btn btn-secondary" onclick="window.modernUI.toggleTheme()">
                        <i class="fas fa-moon"></i>
                        Changer Thème
                    </button>
                    <button class="btn btn-success" onclick="window.advancedUX.showAnalysisWizard()">
                        <i class="fas fa-magic"></i>
                        Assistant
                    </button>
                    <button class="btn btn-warning" onclick="testTooltip(this)" data-tooltip="Ceci est un tooltip de démonstration">
                        <i class="fas fa-info"></i>
                        Tooltip
                    </button>
                </div>
            </div>
            
            <div class="demo-card">
                <div class="demo-title">
                    <i class="fas fa-keyboard"></i>
                    Raccourcis Clavier
                </div>
                <div class="text-sm">
                    <div class="mb-2"><kbd>Ctrl</kbd> + <kbd>K</kbd> - Recherche</div>
                    <div class="mb-2"><kbd>Ctrl</kbd> + <kbd>D</kbd> - Tableau de bord</div>
                    <div class="mb-2"><kbd>Ctrl</kbd> + <kbd>A</kbd> - Automatisation</div>
                    <div class="mb-2"><kbd>Ctrl</kbd> + <kbd>T</kbd> - Changer thème</div>
                    <div class="mb-2"><kbd>Ctrl</kbd> + <kbd>Shift</kbd> + <kbd>A</kbd> - Assistant analyse</div>
                    <div class="mb-2"><kbd>Escape</kbd> - Fermer modales</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Indicateurs de chargement -->
    <div class="demo-section">
        <h2>⏳ Indicateurs de Chargement</h2>
        <div class="component-showcase">
            <div class="demo-card">
                <div class="demo-title">
                    <i class="fas fa-spinner"></i>
                    Spinners
                </div>
                <div class="flex items-center gap-6">
                    <div class="loading-spinner"></div>
                    <div class="loading-spinner-lg"></div>
                    <div class="loading-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </div>
            </div>
            
            <div class="demo-card">
                <div class="demo-title">
                    <i class="fas fa-tasks"></i>
                    Barres de Progression
                </div>
                <div class="space-y-4">
                    <div class="progress">
                        <div class="progress-bar" style="width: 25%"></div>
                    </div>
                    <div class="progress">
                        <div class="progress-bar" style="width: 60%"></div>
                    </div>
                    <div class="progress">
                        <div class="progress-bar animated" style="width: 80%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Créer des graphiques de démonstration
    createDemoCharts();
});

function createDemoCharts() {
    // Données de démonstration
    const priceData = {
        labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun'],
        prices: [100, 105, 98, 110, 115, 120]
    };
    
    const allocationData = {
        labels: ['Actions', 'Obligations', 'Crypto', 'Cash'],
        values: [60, 25, 10, 5]
    };
    
    // Créer les graphiques
    if (window.tradingCharts) {
        window.tradingCharts.createPriceChart('priceChart', priceData, {
            label: 'Prix de Démonstration'
        });
        
        window.tradingCharts.createAllocationChart('allocationChart', allocationData);
    }
}

function testTooltip(element) {
    showNotification('Tooltip testé! Survolez le bouton pour voir le tooltip.', 'info');
}

// Démonstration des notifications
setTimeout(() => {
    showNotification('Bienvenue dans l\'interface moderne de TradingAgents!', 'success', 8000);
}, 1000);
</script>
{% endblock %}
