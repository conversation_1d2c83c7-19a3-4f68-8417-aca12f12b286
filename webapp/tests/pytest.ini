[tool:pytest]
# Configuration pytest pour TradingAgents

# Répertoires de tests
testpaths = tests

# Patterns de fichiers de tests
python_files = test_*.py *_test.py

# Patterns de classes de tests
python_classes = Test*

# Patterns de fonctions de tests
python_functions = test_*

# Marqueurs personnalisés
markers =
    slow: marque les tests comme lents (désactivés par défaut)
    integration: tests d'intégration
    e2e: tests end-to-end
    performance: tests de performance
    browser: tests nécessitant un navigateur
    api: tests d'API
    frontend: tests frontend
    backend: tests backend

# Options par défaut
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes

# Timeout pour les tests (en secondes)
timeout = 300

# Répertoires à ignorer
norecursedirs = 
    .git
    .tox
    dist
    build
    *.egg
    __pycache__
    .pytest_cache

# Variables d'environnement pour les tests
env = 
    TESTING = true
    FLASK_ENV = testing
    WTF_CSRF_ENABLED = false

# Filtres d'avertissements
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
