# Dépendances pour les tests TradingAgents Interface Moderne

# Framework de tests
pytest>=7.0.0
pytest-html>=3.1.0
pytest-cov>=4.0.0
pytest-xdist>=3.0.0
pytest-timeout>=2.1.0
pytest-mock>=3.10.0

# Tests d'interface utilisateur
selenium>=4.15.0
webdriver-manager>=4.0.0

# Tests de performance et charge
requests>=2.31.0
aiohttp>=3.8.0

# Utilitaires de test
coverage>=7.0.0
mock>=4.0.0
responses>=0.23.0

# Analyse de code
flake8>=6.0.0
black>=23.0.0
isort>=5.12.0

# Documentation des tests
pytest-html>=3.1.0
pytest-json-report>=1.5.0

# Tests spécifiques Flask
pytest-flask>=1.2.0

# Outils de debugging
pdb++>=0.10.0
ipdb>=0.13.0

# Validation de données
jsonschema>=4.17.0
cerberus>=1.3.0

# Mocking et fixtures
factory-boy>=3.2.0
faker>=19.0.0

# Tests de sécurité
bandit>=1.7.0
safety>=2.3.0

# Mesure de performance
memory-profiler>=0.60.0
psutil>=5.9.0

# Utilitaires système
python-dotenv>=1.0.0
click>=8.1.0
