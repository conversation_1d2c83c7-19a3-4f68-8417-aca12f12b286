
# 🎨 Guide de Style TradingAgents

## Templates
- ✅ Utiliser `{% extends "base_modern.html" %}`
- ✅ Structurer avec `<div class="container">`
- ✅ Utiliser les en-têtes de section avec `.section-header`

## Composants Principaux
- 📊 Métriques: `.metric-card`, `.metrics-grid`
- 📈 Graphiques: `.chart-container`, `.chart-wrapper`
- 🃏 Cartes: `.card`, `.card-header`, `.card-body`
- 🔘 Boutons: `.btn`, `.btn-primary`, `.btn-secondary`
- 📝 Formulaires: `.form-group`, `.form-control`, `.form-label`

## Classes Utilitaires
- 🎯 Layout: `.grid`, `.flex`, `.container`
- 📏 Espacement: `.mb-4`, `.mt-6`, `.p-4`
- 🎨 Couleurs: `.text-primary`, `.bg-card`, `.border-color`
- 📱 Responsive: `.md:grid-cols-2`, `.lg:col-span-3`

## Variables CSS Importantes
- Couleurs: `--primary-color`, `--bg-card`, `--text-primary`
- Espacement: `--space-4`, `--space-6`, `--space-8`
- Bordures: `--radius-md`, `--radius-lg`, `--border-color`
- Transitions: `--transition-fast`, `--transition-normal`

## À Éviter
- ❌ Classes Bootstrap: `.col-md-`, `.me-2`, `.mb-3`
- ❌ Styles inline excessifs
- ❌ Couleurs hardcodées
- ❌ Tailles fixes non responsive
