#!/usr/bin/env python3
"""
Script de démarrage pour TradingAgents Web Interface
"""

import os
import sys
from pathlib import Path

# Ajouter le répertoire parent au path pour importer TradingAgents
current_dir = Path(__file__).parent
parent_dir = current_dir.parent
sys.path.insert(0, str(parent_dir))

# Vérifier les variables d'environnement requises
required_env_vars = ['OPENAI_API_KEY', 'FINNHUB_API_KEY']
missing_vars = []

for var in required_env_vars:
    if not os.getenv(var):
        missing_vars.append(var)

if missing_vars:
    print("⚠️  Variables d'environnement manquantes:")
    for var in missing_vars:
        print(f"   - {var}")
    print("\nVeuillez définir ces variables avant de démarrer l'application.")
    print("Exemple:")
    for var in missing_vars:
        print(f"export {var}=your_api_key_here")
    sys.exit(1)

# Importer et démarrer l'application
try:
    from app import app, socketio
    
    print("🚀 Démarrage de TradingAgents Web Interface...")
    print("📊 Interface disponible sur: http://localhost:5000")
    print("🔧 Mode debug activé")
    print("💡 Appuyez sur Ctrl+C pour arrêter")
    
    # Démarrer l'application
    socketio.run(
        app, 
        debug=True, 
        host='0.0.0.0', 
        port=5000,
        allow_unsafe_werkzeug=True
    )
    
except ImportError as e:
    print(f"❌ Erreur d'importation: {e}")
    print("Assurez-vous que toutes les dépendances sont installées:")
    print("pip install -r requirements.txt")
    sys.exit(1)
    
except Exception as e:
    print(f"❌ Erreur lors du démarrage: {e}")
    sys.exit(1)
