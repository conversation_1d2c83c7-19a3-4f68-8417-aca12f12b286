/* ========================================
   Navigation Moderne TradingAgents
   ======================================== */

/* Barre de navigation principale */
.navbar {
  background-color: var(--bg-card);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  transition: all var(--transition-normal);
}

.navbar-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4) var(--space-6);
  max-width: 1400px;
  margin: 0 auto;
}

/* Logo et branding */
.navbar-brand {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--text-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.navbar-brand:hover {
  color: var(--primary-color);
  text-decoration: none;
}

.navbar-brand-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--text-lg);
}

/* Navigation principale */
.navbar-nav {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  position: relative;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  position: relative;
}

.nav-link:hover {
  color: var(--primary-color);
  background-color: var(--bg-secondary);
  text-decoration: none;
}

.nav-link.active {
  color: var(--primary-color);
  background-color: var(--primary-light);
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background-color: var(--primary-color);
  border-radius: var(--radius-full);
}

/* Indicateurs de statut */
.nav-status-indicator {
  position: absolute;
  top: var(--space-2);
  right: var(--space-2);
  width: 8px;
  height: 8px;
  border-radius: var(--radius-full);
  border: 2px solid var(--bg-card);
}

.nav-status-indicator.status-running {
  background-color: var(--success-color);
  animation: pulse 2s infinite;
}

.nav-status-indicator.status-stopped {
  background-color: var(--error-color);
}

.nav-status-indicator.status-warning {
  background-color: var(--warning-color);
}

/* Actions de navigation */
.navbar-actions {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

/* Bouton de thème */
.theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.theme-toggle:hover {
  background-color: var(--bg-secondary);
  border-color: var(--border-hover);
  color: var(--primary-color);
}

/* Menu mobile */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  gap: 4px;
  width: 24px;
  height: 24px;
  cursor: pointer;
  padding: 0;
  border: none;
  background: transparent;
}

.mobile-menu-toggle span {
  display: block;
  width: 100%;
  height: 2px;
  background-color: var(--text-primary);
  border-radius: var(--radius-full);
  transition: all var(--transition-fast);
}

.mobile-menu-toggle.active span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Menu mobile overlay */
.mobile-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-overlay);
  z-index: var(--z-modal);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.mobile-menu.active {
  opacity: 1;
  visibility: visible;
}

.mobile-menu-content {
  position: absolute;
  top: 0;
  right: 0;
  width: 280px;
  height: 100%;
  background-color: var(--bg-card);
  box-shadow: var(--shadow-xl);
  transform: translateX(100%);
  transition: transform var(--transition-normal);
  overflow-y: auto;
}

.mobile-menu.active .mobile-menu-content {
  transform: translateX(0);
}

.mobile-menu-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.mobile-menu-nav {
  padding: var(--space-4);
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-4);
  color: var(--text-primary);
  text-decoration: none;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  margin-bottom: var(--space-2);
}

.mobile-nav-link:hover {
  background-color: var(--bg-secondary);
  color: var(--primary-color);
  text-decoration: none;
}

.mobile-nav-link.active {
  background-color: var(--primary-light);
  color: var(--primary-color);
}

/* Breadcrumbs */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4) 0;
  font-size: var(--text-sm);
  color: var(--text-muted);
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.breadcrumb-item:not(:last-child)::after {
  content: '/';
  color: var(--text-muted);
  margin-left: var(--space-2);
}

.breadcrumb-link {
  color: var(--text-secondary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.breadcrumb-link:hover {
  color: var(--primary-color);
  text-decoration: none;
}

.breadcrumb-current {
  color: var(--text-primary);
  font-weight: 500;
}

/* Recherche globale */
.global-search {
  position: relative;
  width: 300px;
}

.global-search-input {
  width: 100%;
  padding: var(--space-3) var(--space-4) var(--space-3) var(--space-10);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-size: var(--text-sm);
  transition: all var(--transition-fast);
}

.global-search-input:focus {
  outline: none;
  border-color: var(--border-focus);
  background-color: var(--bg-primary);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.global-search-icon {
  position: absolute;
  left: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  font-size: var(--text-sm);
}

.global-search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  margin-top: var(--space-1);
  max-height: 400px;
  overflow-y: auto;
  z-index: var(--z-dropdown);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all var(--transition-fast);
}

.global-search-results.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .navbar-nav {
    display: none;
  }
  
  .mobile-menu-toggle {
    display: flex;
  }
  
  .global-search {
    width: 200px;
  }
  
  .navbar-actions {
    gap: var(--space-2);
  }
}

@media (max-width: 640px) {
  .navbar-container {
    padding: var(--space-3) var(--space-4);
  }
  
  .global-search {
    display: none;
  }
  
  .navbar-brand {
    font-size: var(--text-lg);
  }
}
